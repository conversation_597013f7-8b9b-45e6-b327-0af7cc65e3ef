<script setup>
import {onUnmounted} from "vue";
import SingleEquipmentSelection from "@/views/pages/online/sidebar/property/single-equipment-selection"
import {useIframeCommunication} from "@/hooks/useIframeCommunication.js";
import {ElMessage} from "element-plus";
import useAppStore from "@/store/modules/app";
const appStore = useAppStore();
const {sendMessage, cleanup} = useIframeCommunication()

const cadAppRef = inject('cadAppRef');

const visible = ref(false)

const currentId = ref(null)

const sendCommand = () => {
  console.log("🚀 ~ sendCommand ~  appStore.property:",  appStore.property)
  // visible.value=true
  // currentId.value = '4663960'
  
  const params = {
    name: '设备选型',
    content: '|Mx_ModelSelection',
    type: "sdkcad",
  }
  sendMessage(cadAppRef.value, params, (res) => {
    console.log('sendCommandres', res)
    if(res.id) {
      currentId.value = res.id
      visible.value = true
    }
    if(res.type=='error'){
      if(appStore.property&&appStore.property.length===1){
    visible.value=true
    currentId.value=appStore.property[0]
  }else{
    ElMessage.info('请选择设备')
  }
    }
  })
}

onMounted(() => {
  sendCommand()
})

onUnmounted(() => {
  console.log('onUnmounted设备选型')
})
// watch(
//   () => appStore.property,
//   (newInfo, oldInfo) => {
//     console.log("🚀 ~ newInfo:", newInfo)
//   })
</script>

<template>
  <!--    设备选型-->
  <single-equipment-selection
      v-if="visible"
      :id="currentId"
      v-model:visible="visible"
  />
</template>

<style lang="scss" scoped>

</style>
