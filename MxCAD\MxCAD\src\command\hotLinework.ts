import {Mx<PERSON><PERSON>,McEdGetPointWorldDrawObject,MxDbLeadComment,MrxDbgUiPrPoint} from "mxdraw";
import {MxCADResbuf, MxCADUtility, McDb,  MxCpp, McDbText, MxCADUiPrPoint, MxCADSelectionSet, McDbMText, McDbLine, McGePoint3d} from 'mxcad'




async function Mx_hotLineWork(data) {
    const labelList = JSON.parse(data)
    console.log('labelList', labelList)
    const mxcad = MxCpp.getCurrentMxCAD();

      const drawNextLabel = async (index) => {
        // if (index >= labelList.length) return;
        let currentData =''
        for (let index = 0; index <  labelList.length; index++) {
            if(index==0){
                currentData=labelList[index].moduleName
            }else{
                currentData=currentData+'\\P'+labelList[index].moduleName
            }
        }
        console.log(index + 1, '正在标注');

        const getPoint = new MrxDbgUiPrPoint();
        getPoint.setMessage("\n指定标注位置:");

        getPoint.go((status) => {
            if (status !== 0) {
                console.log("取消或错误");
                return;
            }

            const pt1 = getPoint.value();
            let leadComment = new MxDbLeadComment();
            leadComment.point1 = pt1.clone();
            leadComment.textHeight = MxFun.screenCoordLong2Doc(50);
            leadComment.text = currentData;
            leadComment.textWidth = MxFun.screenCoordLong2Doc(300);

            leadComment.fixedSize = true;
            if (leadComment.fixedSize) {
                leadComment.textHeight = 20;
                leadComment.textWidth = 250;
            }
            leadComment.color = mxcad.getCurrentDatabaseDrawColor();

            const worldDrawComment = new McEdGetPointWorldDrawObject();
            worldDrawComment.setDraw((currentPoint: THREE.Vector3, pWorldDraw) => {
                leadComment.point2 = currentPoint;
                pWorldDraw.drawCustomEntity(leadComment);
            });

            getPoint.setBasePt(pt1);
            getPoint.setUseBasePt(true);
            getPoint.setUserDraw(worldDrawComment);
            getPoint.setMessage("\n指定第二点:");

            getPoint.go((status) => {
                if (status !== 0) {
                    console.log(status);
                    return;
                }

                const pt2 = getPoint.value();
                leadComment.point2 = pt2;
                MxFun.addToCurrentSpace(leadComment);

                // 当前标注完成，递归进入下一个
                // drawNextLabel(index + 1);
            });
        });
    };

    // 启动递归标注
    drawNextLabel(0);
}


export function init() {
    MxFun.addCommand("Mx_hotLineWork", Mx_hotLineWork);
}
