import {Mx<PERSON><PERSON>,McEdGetPointWorldDrawObject,MrxDbgUiPrPoint} from "mxdraw";
import {MxCADResbuf, MxCADUtility, McDb,  MxCpp, McDbText, MxCADUiPrPoint, MxCADSelectionSet, McDbMText, McDbLine, McGePoint3d, McCmColor} from 'mxcad'




async function Mx_hotLineWork(data) {
    const labelList = JSON.parse(data)
    console.log('labelList', labelList)
    const mxcad = MxCpp.getCurrentMxCAD();

      const drawNextLabel = async (index) => {
        // if (index >= labelList.length) return;
        let currentData =''
        for (let index = 0; index <  labelList.length; index++) {
            if(index==0){
                currentData=labelList[index].moduleName
            }else{
                currentData=currentData+'\\P'+labelList[index].moduleName
            }
        }
        console.log(index + 1, '正在标注');

        const getPoint = new MrxDbgUiPrPoint();
        getPoint.setMessage("\n指定标注位置:");

        getPoint.go((status) => {
            if (status !== 0) {
                console.log("取消或错误");
                return;
            }

            const pt1 = getPoint.value();

            // 创建多行文本对象
            const mText = new McDbMText();
            mText.contents = currentData; // 使用\\P换行符的文本
            mText.textHeight = 20;
            mText.width = 250;
            mText.attachment = McDb.AttachmentPoint.kMiddleLeft;
            // 设置文本颜色为黑色
            mText.trueColor = new McCmColor(0, 0, 0);

            const worldDrawComment = new McEdGetPointWorldDrawObject();
            worldDrawComment.setDraw((currentPoint: THREE.Vector3, pWorldDraw) => {
                // 绘制引线
                const line = new McDbLine();
                line.startPoint = new McGePoint3d(pt1.x, pt1.y, pt1.z);
                line.endPoint = new McGePoint3d(currentPoint.x, currentPoint.y, currentPoint.z);
                line.trueColor = mxcad.getCurrentDatabaseDrawColor();
                pWorldDraw.drawEntity(line);

                // 绘制多行文本
                mText.location = new McGePoint3d(currentPoint.x, currentPoint.y, currentPoint.z);
                pWorldDraw.drawEntity(mText);
            });

            getPoint.setBasePt(pt1);
            getPoint.setUseBasePt(true);
            getPoint.setUserDraw(worldDrawComment);
            getPoint.setMessage("\n指定第二点:");

            getPoint.go((status) => {
                if (status !== 0) {
                    console.log(status);
                    return;
                }

                const pt2 = getPoint.value();

                // 绘制最终的引线
                const finalLine = new McDbLine();
                finalLine.startPoint = new McGePoint3d(pt1.x, pt1.y, pt1.z);
                finalLine.endPoint = new McGePoint3d(pt2.x, pt2.y, pt2.z);
                finalLine.trueColor = mxcad.getCurrentDatabaseDrawColor();
                mxcad.drawEntity(finalLine);

                // 绘制最终的多行文本
                mText.location = new McGePoint3d(pt2.x, pt2.y, pt2.z);
                mxcad.drawEntity(mText);

                // 当前标注完成，递归进入下一个
                // drawNextLabel(index + 1);
            });
        });
    };

    // 启动递归标注
    drawNextLabel(0);
}


export function init() {
    MxFun.addCommand("Mx_hotLineWork", Mx_hotLineWork);
}
