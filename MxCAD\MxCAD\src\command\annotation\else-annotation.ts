import { Mx<PERSON><PERSON>,McEdGetPointWorldDrawObject,MxDbLeadComment,MrxDbgUiPrPoint } from "mxdraw";
import {
  MxCADUiPrKeyWord,
  MxCADUtility,
  McDb,
  MxCpp,
  McDbText,
  MxCADUiPrPoint,
  MxCADSelectionSet,
  MxCADResbuf,
  MxCADUiPrEntity,
  McDbEntity,
  McDbMText,
  
} from "mxcad";

let keyVal: string = "";
async function elseAnnotation(data) {
  console.log("elseAnnotation", data);
  const getKey = new MxCADUiPrKeyWord();
  getKey.setMessage("输入绘制类型");
  getKey.setKeyWords(
    "[自由标注(1)/线路长度标注(2)/图纸说明(3)/电缆通道长度标注(4)]"
  );
  keyVal = await getKey.go();
  console.log("keyVal", keyVal);
  switch (keyVal) {
    case "1":
      MxFun.sendStringToExecute("_DrawText");
      break;
    case "2":
      await lineLengthAnnotation(data);
      break;
    case "3":
      await drawingExplain(data);
      break;
    case "4":
      await drawCablePassageLabelHandle(data);
      break;
  }
}

async function drawCablePassageLabelHandle(data) {
  const filter = new MxCADResbuf();
  // 添加对象类型，选择集只选择文字类型的对象
  //     filter.AddMcDbEntityTypes("TEXT,MTEXT")
  let ids = await MxCADUtility.userSelect("框选需要的对象", filter);
  console.log(ids);
  if (!ids.length) return;
  const idList = ids.map((id) => {
    const ent = id.getMcDbEntity();
    const equipmentId = ent.getxDataString("equipmentId");
    console.log("equipmentId", equipmentId);
    if (equipmentId.ret) return equipmentId.val;
    return id.id;
  });
  const params = {
    idList,
    keyVal,
  };
  MxFun.postMessageToParentFrame({ messageId: data.id, params });
}

async function drawCablePassageLabel(data) {
  const labelList = data.param.labelList;
  console.log("labelList", labelList);
  const mxcad = MxCpp.getCurrentMxCAD();

  const text = new McDbText();

  let index = 0;
  const cableDataLength = labelList.length;
  while (true) {
    index += 1;
    const getNextPoint = new MxCADUiPrPoint();
    getNextPoint.setMessage("选择引线基准点:");
    let nextPt = await getNextPoint.go();
    console.log("nextPt", nextPt, index);
    if (!nextPt) return;

    const currentData = labelList[index - 1];

    console.log("currentData", currentData);

    const textString = `${currentData}`;

    text.widthFactor = 1;
    text.horizontalMode = McDb.TextHorzMode.kTextCenter;
    text.verticalMode = McDb.TextVertMode.kTextBottom;
    text.textString = textString;
    text.position = text.alignmentPoint = nextPt;
    text.height = 20;
    mxcad.drawEntity(text);

    if (cableDataLength === index) return;
  }
}

async function lineLengthAnnotation(data) {
  const entList: any[] = [];
  while (true) {
    let selEntity = new MxCADUiPrEntity();
    if (entList.length > 0) {
      selEntity.setMessage("选择终点设备");
    } else {
      selEntity.setMessage("选择起始设备");
    }
    let id = await selEntity.go();
    const ent = id.getMcDbEntity();
    if (!ent) return;
    const equipmentId = ent.getxDataString("equipmentId");
    console.log("equipmentId", equipmentId);
    if (equipmentId.ret) {
      entList.push(equipmentId.val);
    } 
    if (entList.length === 2) break;
  }
  const params = {
    idList: entList,
    keyVal,
  };
  MxFun.postMessageToParentFrame({ messageId: data.id, params });
}

async function drawLengthAnnotation(data) {
  const textString = data.param.text;
  console.log("text", textString);
  const mxcad = MxCpp.getCurrentMxCAD();

  const text = new McDbText();

  // const getNextPoint = new MxCADUiPrPoint();
  // getNextPoint.setMessage("选择引线基准点:");
  // let nextPt = await getNextPoint.go();
  // if (!nextPt) return;
  
  const drawNextLabel = async (index) => {
    // if (index >= textString.length) return;

    const currentData = textString
    console.log(index + 1, '正在标注');

    const getPoint = new MrxDbgUiPrPoint();
    getPoint.setMessage("\n指定标注位置:");

    getPoint.go((status) => {
        if (status !== 0) {
            console.log("取消或错误");
            return;
        }

        const pt1 = getPoint.value();
        let leadComment = new MxDbLeadComment();
        leadComment.point1 = pt1.clone();
        leadComment.textHeight = MxFun.screenCoordLong2Doc(50);
        leadComment.text = currentData;
        leadComment.textWidth = MxFun.screenCoordLong2Doc(300);

        leadComment.fixedSize = true;
        if (leadComment.fixedSize) {
            leadComment.textHeight = 20;
            leadComment.textWidth = 150;
        }
        leadComment.color = mxcad.getCurrentDatabaseDrawColor();

        const worldDrawComment = new McEdGetPointWorldDrawObject();
        worldDrawComment.setDraw((currentPoint: THREE.Vector3, pWorldDraw) => {
            leadComment.point2 = currentPoint;
            pWorldDraw.drawCustomEntity(leadComment);
        });

        getPoint.setBasePt(pt1);
        getPoint.setUseBasePt(true);
        getPoint.setUserDraw(worldDrawComment);
        getPoint.setMessage("\n指定第二点:");

        getPoint.go((status) => {
            if (status !== 0) {
                console.log(status);
                return;
            }

            const pt2 = getPoint.value();
            leadComment.point2 = pt2;
            MxFun.addToCurrentSpace(leadComment);

            // 当前标注完成，递归进入下一个
            // drawNextLabel(index + 1);
        });
    });
};

// 启动递归标注
drawNextLabel(0);
  // text.widthFactor = 1;
  // text.horizontalMode = McDb.TextHorzMode.kTextCenter;
  // text.verticalMode = McDb.TextVertMode.kTextBottom;
  // text.textString = textString;
  // text.position = text.alignmentPoint = nextPt;
  // text.height = 20;
  // mxcad.drawEntity(text);
}

const drawingExplain = async (data) => {
  const params = {
    keyVal,
  };
  MxFun.postMessageToParentFrame({ messageId: data.id, params });
};

function insertP(str) {
  let result = "";
  for (let i = 0; i < str.length; i += 50) {
    const segment = str.slice(i, i + 50);
    result += segment + " \\P ";
  }
  return result.slice(0, -2); // 去除末尾多余的\\P
}

const drawDrawingExplain = async (data) => {
  //   const textString = insertP(data.param.text);
  const textString = data.param.text;
  console.log("text", textString);
  const mxcad = MxCpp.getCurrentMxCAD();

  const mText = new McDbMText();

  const getNextPoint = new MxCADUiPrPoint();
  getNextPoint.setMessage("选择引线基准点:");
  let nextPt = await getNextPoint.go();
  if (!nextPt) return;

  mText.width = 400;
  mText.textHeight = 10; // 设置文本高度
  mText.attachment = McDb.AttachmentPoint.kMiddleLeft; // 设置文本对齐方式
  mText.contents = textString; // 设置文本内容
  mText.location = nextPt; // 设置文本位置
  mxcad.drawEntity(mText);
};

export function init() {
  MxFun.addCommand("Else_Annotation", elseAnnotation);
  MxFun.addCommand("Draw_Cable_Passage_Label", drawCablePassageLabel);
  MxFun.addCommand("Draw_Length_Annotation", drawLengthAnnotation);
  MxFun.addCommand("Draw_Drawing_Explain", drawDrawingExplain);
}
