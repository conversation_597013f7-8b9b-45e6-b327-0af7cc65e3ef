{"name": "ruoyi", "version": "3.8.8", "description": "若依管理系统", "author": "若依", "license": "MIT", "type": "module", "scripts": {"dev": "vite", "devCAD": "vite preview --port 3366 --outDir ./MxCAD/dist", "build:prod": "vite build", "build:stage": "vite build --mode staging", "build:staging:external": "vite build --mode staging.external", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@fontsource/noto-sans-sc": "^5.1.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vueup/vue-quill": "1.2.0", "@vueuse/core": "^10.11.0", "axios": "0.28.1", "clipboard": "2.0.11", "echarts": "5.5.1", "element-plus": "^2.9.1", "file-saver": "2.0.5", "fuse.js": "6.6.2", "js-beautify": "1.14.11", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "mitt": "^3.0.1", "mxcad": "^1.0.275", "nprogress": "0.2.0", "pinia": "2.1.7", "splitpanes": "3.1.5", "three": "^0.113.2", "three.js": "^0.77.1", "vue": "3.5.13", "vue-cropper": "1.1.1", "vue-router": "4.4.0", "vuedraggable": "4.1.0", "vuex": "^4.1.0", "vxe-pc-ui": "^4.3.78", "vxe-table": "^4.10.5", "xgplayer": "^3.0.21"}, "devDependencies": {"@vitejs/plugin-vue": "5.0.5", "sass": "1.77.5", "unplugin-auto-import": "0.17.6", "unplugin-vue-setup-extend-plus": "1.0.1", "vite": "5.3.2", "vite-plugin-compression": "0.5.1", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-svg-icons": "2.0.1"}}