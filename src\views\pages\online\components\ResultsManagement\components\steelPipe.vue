<template>
<data-dialog
    dataWidth="1200"
    @close="closeDialog"
    v-if="appStore.mapIndex == '钢管杆明细表'"
  >
  <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        钢管杆明细表
      </h4>
    </template>
    <template #body>
        <div class="material">
        <div style="margin-left: 10px">
          <el-button icon="Paperclip" type="text" @click="exportExcel">生成报表</el-button>
        </div>
        <el-table :data="tableData" :row-class-name="setRowClassName" border class="input-table" height="40vh" size="small" style="width: 100%" @selection-change="handleSelectionChangeWl">
          <el-table-column :selectable="selectable" align="center"  type="selection" width="50px"></el-table-column>
          <el-table-column :selectable="selectable" align="center" label="杆型" prop="RodType" ></el-table-column>
          <el-table-column align="center" label="单基总重(kg)" prop="GrossWeightOfSingleBase" >
          </el-table-column>
          <el-table-column align="center" label="杆塔Ⅰ段(kg)" >
            <el-table-column align="center" label="杆塔Ⅰ段(kg)" prop="GD1"></el-table-column>
            <el-table-column align="center" label="螺栓脚钉垫圈重(kg)" prop="DJLS"></el-table-column>
          </el-table-column>
          <el-table-column align="center" label="杆塔Ⅱ段(kg)" >
            <el-table-column align="center" label="总重(kg)" prop="GD2"></el-table-column>
          </el-table-column>
          <el-table-column align="center" label="爬梯重(kg)" prop="PTZL"></el-table-column>
          <el-table-column align="center" label="横担总重(kg)" prop="CrossArm"></el-table-column>
          <el-table-column align="center" label="基数" prop="Base"></el-table-column>
          <el-table-column align="center" label="总重(kg)" prop="FinalGrossWeight"></el-table-column>
        </el-table>
      </div>
    </template>
  </data-dialog>
</template>
<script setup> 
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
import { useRoute } from "vue-router";
import {
    getGggMx,generatorGggmxReport
} from "@/api/insertSag/index.js";
import {
  autoMatchingPreview,
} from "@/api/desginManage/ToolManagement.js";
import { ElLoading } from 'element-plus'
import { inject } from 'vue';
const { proxy } = getCurrentInstance();
const route = useRoute();
const appStore = useAppStore();
const { sendMessage, cleanup } = useIframeCommunication();
const taskId = route.query.id;
const tableData=ref([])
const cadIframeRef = ref(null)
const callChildC = inject('callChildC');

const closeDialog = () => {
  appStore.mapIndex = "";
};
const fileToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file); // 将文件转换为 Base64
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
};
const exportExcel = async () => {
  try {
    // 异步获取数据
//     const res = await autoMatchingPreview({ drawingId: 2714 });

//      // 将文件转换为 Base64
//      const base64Data = await fileToBase64(res);

// // 将 Base64 数据赋值给 tableData.value 的每个元素的 file 属性
// if (Array.isArray(tableData.value)) {
//   tableData.value = tableData.value.map((element) => ({
//     ...element,
//     file: base64Data, // 使用 Base64 字符串
//   }));
// }

//     console.log("🚀 ~ tableData.value:", tableData.value);

//     // 检查 tableData.value 是否为空
    if (tableData.value.length === 0) {
      proxy.$message.warning("目前暂无数据可生成");
      return; // 如果为空，直接返回
    }

    // 发送消息
    oniframeMessage({
      type: "cadPreview",
      content: "Mx_gggcl",
      formData: {
        content: "钢管杆明细表",
        tableData: JSON.stringify(tableData.value),
        countyOrganisationName: route.query.countyOrganisationName,
        projectName: route.query.projectName,
        stage: route.query.stage,
        proCode: route.query.proCode,
      },
    });
  } catch (error) {
    console.error("🚀 ~ exportExcel ~ error:", error);
    proxy.$message.error("数据生成失败，请稍后重试");
  }
};
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = appStore.iframeHide
  console.log("🚀 ~ oniframeMessage ~ appStore.iframe:", appStore.iframe)
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
const queryList=()=>{
    getGggMx(taskId).then(res=>{
        // console.log("🚀 ~ getGggMx ~ res:", res)
        tableData.value=res.data
    })
}
// 事件处理函数
const handleMessage = (event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '钢管杆明细表') {
      const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      const files = event.data.params.formData.files
      const tableData = JSON.parse(event.data.params.formData.tableData)
      const tableDataNew = tableData.map(item => {
        return {
          gx:item.RodType,
          djzz:item.GrossWeightOfSingleBase,
          gtyd:item.GD1,
          lsjddqz:item.DJLS,
          gtedzz:item.GD2,
          ptz:item.PTZL,
          hdzz:item.CrossArm,
          js:item.Base,
          zz:item.FinalGrossWeight
        }
      })
      const fileFormData = new FormData()
      files.forEach(item => {
        fileFormData.append('multipartFile', item)
      })
      fileFormData.append("gggmxList", JSON.stringify(tableDataNew));
      fileFormData.append('prjTaskInfoId', route.query.id)
      fileFormData.append('stage', route.query.stage)
      generatorGggmxReport(fileFormData).then(res => {
        if (res.code === 200) {
          proxy.$message.success("保存成功");
          loading.close()
          if (callChildC) {
          callChildC();
        }
        } else {
          proxy.$message.error(res.msg);
          loading.close()
        }
      })
    }
  }
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "钢管杆明细表") {
      queryList();
    }
  }
);
</script>
<style lang="scss" scoped>
@use '../../index' as *;
</style>