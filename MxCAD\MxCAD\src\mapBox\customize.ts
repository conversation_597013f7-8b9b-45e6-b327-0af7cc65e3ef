import {MxFun} from "mxdraw";
import {ref} from "vue";
import {Mc<PERSON><PERSON>, McDbLine, McGe<PERSON>oint3d, McCmColor, MxCpp} from "mxcad"
import {useMessageHandlers} from './hooks/useMapping';
import SDKClickInfo from "./sdkClickInfo";
import {getLogSetting} from  '../util/logSetting'
import {HzgctuBlockFun, drawLine, drawMLine} from "./draw/components/Hzgctu";
import {BlockFun} from "./draw/components/drawbock";
import {calculateNewCoordinate, colorHexToRgb, convertDegreesToRadians} from "../utils/mx-cad";
import {addLayer} from "./draw/components/layer";
let mapType = '01' // 01: 内网SDK地图  02: 思极地图
const urlParams = new URLSearchParams(new URL(window.location.href).search)
const xtoken = urlParams.get('token') || 'AjQxMTABACAyODVENDcwOUU5NkE0RkY2OTNEQTYzRDIyQTM3M0UyNAIAAAMAAAQADjIwMjUwMTIxMDkyNTE5BQAEAAKjAAcABAAAAAEKADgyODVENDcwOUU5NkE0RkY2OTNEQTYzRDIyQTM3M0UyNDE3Mzc0NTE1MTkxMDgtMTEyMDMwMjQyOP8BnjCCAZoGCSqGSIb3DQEHAqCCAYswggGHAgEBMQswCQYFKw4DAhoFADALBgkqhkiG9w0BBwExggFmMIIBYgIBATBjMFsxCzAJBgNVBAYTAmNuMQswCQYDVQQIEwJ6ajELMAkGA1UEBxMCaHoxDzANBgNVBAoTBkh1YXl1bjENMAsGA1UECxMEY2lzcDESMBAGA1UEAxMJbG9jYWxob3N0AgQ%2Fg56HMAkGBSsOAwIaBQCgXTAYBgkqhkiG9w0BCQMxCwYJKoZIhvcNAQcBMBwGCSqGSIb3DQEJBTEPFw0yNTAxMjEwOTI1MTlaMCMGCSqGSIb3DQEJBDEWBBQrWNf1itZSnwSmqs1b6WBzROis5jALBgkqhkiG9w0BAQUEgYA%2F3JbAmrGcufF54aBBagDDxAch44YTnl3lS%2FNSMg0ndXD1ODNZwZ3N%2FmI3Q%2BhtgUkxOG41da3AR8VDN49xZ%2BeP89YsKstP8q03xl6X9gA3U6C04i9yA4GdJRlNdmcBBQLrzsAO9EaY6jL50K5AvqM13jPjEC6YVO3u9hlxC1barw%3D%3D'
const lineInfos = JSON.parse(decodeURIComponent(urlParams.get('lineInfos')))
const wgBorders = JSON.parse(decodeURIComponent(urlParams.get('wgBorders')))
let rastermap = null
let mapOrCad = false
let Hzgctu=[]//子页面获取的沿布数据
var mapSDKType = ''
// 根据工程阶段得对应sdk地图阶段
const getSDKBusinessKeyByEngStage = {
    '2': '101', //可研
    '3': '202', //初设
    '4': '203', //施工
    '5': '2031', //施工变更
    '6': '204' //竣工
}
const mod = urlParams.get('mod')
// 工程信息
const taskid = urlParams.get('id')
const engStage = urlParams.get('stage')
const engProcessId = urlParams.get('proProcessId') ?? ''//全流程ID
const engProCode = urlParams.get('proCode') ?? ''//项目编码
const engCode = urlParams.get('engCode') ?? ''//单体工程编码
const sdkBusinessNo = engProcessId//engProcessId + engProCode + engCode

const sdkBusinessKey = getSDKBusinessKeyByEngStage[engStage] ?? '20x'
export const formData = ref({})

let messageEvent = null
let messageCopy = null
window.addEventListener('message', (event) => {
    console.log('父页面问候:', event.data,event);
    if(event.data.content==='SDK_add_soil_line'){
        messageCopy=event
    }
    if(event.data.type==="getCableCreateInfoFromPipe"){
        messageCopy.data.type='greeting';
        messageCopy.data.content='SDK_add_soil_line';
        messageCopy.data.options=event.data.params;
    }
    console.log('mapOrCad', mapOrCad);
    messageEvent = event
    console.log(event.data.type,'typeeeee')
    if (event.data.type === 'onlycad') {// 调用CAD方法
        if (mapOrCad) {
            sendMessage({type: 'showMessage', params: {content: '请切换到CAD页面!'}}, event)
        } else {
            console.log('接收到父级页面上数据：', event.data.options)
            
            formData.value = event.data.options
            if(event.data.options?.name==='JGFZH_Draw'){
                MxFun.sendStringToExecute(event.data.content,event.data.options.tableData?event.data.options.tableData:event.data)
            }else{
                MxFun.sendStringToExecute(event.data.content)
            }
        }
    }else if(event.data.type === 'Huzhi') {
        Hzgctu=event.data.content
        huizhiTu()
    } else if (event.data.type === 'greeting') {
        console.log(event.data.content,'greeting')
        
        // 使用消息处理 Hook，传入地图实例
        //console.log('rastermap', rastermap);
        if (rastermap) {
            const {handleMessage} = useMessageHandlers(rastermap, event);
            handleMessage(event.data.type, event.data.content, event.data.options)
        }
    } else if(event.data.type === 'sdkcad') {
        const cmds = event.data.content.split('|')
        if (mapOrCad) {
            if(!cmds[0]){
                sendMessage({type: 'error', params: {content: '错误'}}, event)
                return
            }
            if (rastermap) {
                const {handleMessage} = useMessageHandlers(rastermap, event);
                handleMessage(event.data.type, cmds[0], event.data.options)
            }
        } else {
            console.log('接收到父级页面上数据：', event.data.options)
            formData.value = event.data.options
            MxFun.sendStringToExecute(cmds[1],event.data)
        }
    }else if(event.data.type==='backgroundcad'){
        formData.value = event.data.options
        MxFun.sendStringToExecute(event.data.content)
    }else if(event.data.type==='getCableCreateInfoFromPipe'){
        if (rastermap) {
            const {handleMessage} = useMessageHandlers(rastermap, messageCopy);
            handleMessage(messageCopy.data.type, messageCopy.data.content, messageCopy.data.options)
        }
    }
})

const sendMessage = (params, myMessageEvent = messageEvent) => { 
    console.log(params,messageEvent,'回调')
    if(getLogSetting("customize_sendMessage"))
        console.log('sendMessage', myMessageEvent?.data, params, messageEvent,'回调方法');

    if(myMessageEvent?.source){
        myMessageEvent.source.postMessage({messageId: myMessageEvent.data?.id, params}, '*')
    } else if(myMessageEvent?.parent){
        myMessageEvent.parent.postMessage({...params}, '*')
    }
}

// 初始加载
export async function init() {
    // 加载地图
    offCmdClick()
    if(mod === "preview"){
        //说明是预览功能
        const config = await MxPluginContext.getUiConfig()
        config.mLeftButtonBarData.isShow = false;
        config.mRightButtonBarData.isShow = false;
        config.isShowFooter = false;
        config.isShowHeader = false;
        config.isShowModelNav = false;

        // const mxdivElements = document.querySelector('#mxdiv');
        // mxdivElements.style.height = 'auto'
        return
    }
    if (mapType === '01') {
        mapSDKLoad()
    } else {
        console.log('加载思极地图')
        mapSJLoad()
    }
}

// 判断SDK地图不能使用cad工具
function offCmdClick() {
    MxFun.addCommand("Mx_Line123", () => {
        if (mapOrCad) {
            alert('请使用地图功能!')
        } else {
            MxFun.sendStringToExecute("Mx_Line")
        }
    });
}
function huizhiTu(){
    deleteLayer()
    console.log('huizhiTu', Hzgctu)
    
for (let index = 0; index < Hzgctu.length; index++) {
    const element = Hzgctu[index];
    try {

    let color = colorHexToRgb(element.drawStyle?.Color)
    let trueColor = null
    if(color) {
        trueColor = new McCmColor(color[0], color[1], color[2])
    }
    // 解析 equipmentInfo
    const equipmentInfo = element.equipmentInfo?JSON.parse(element.equipmentInfo):element.equipmentInfo;
    const positionInfo = equipmentInfo?.positionInfo;
    const topologyrelations =  element.topologyrelations?JSON.parse(element.topologyrelations):element.topologyrelations;
    if(!positionInfo){
        console.warn('此条数据无属性',element)
        continue;
    }
    const coordinate = positionInfo?.points[0].XYZ[0].coordinate
    if(!coordinate) return
    let points = null
    addLayer(element.legendtypekey)
    if(['Line', 'MLine'].includes(positionInfo?.pointType)) {
        points = coordinate.split(';').map(point => {
            const [x, y, z] = point.split(',');
            return {
                x: parseFloat(x),
                y: parseFloat(y),
                z: parseFloat(z)
            };
        });
        const pointLength = points.length
        const {AssociatedIn, AssociatedOut} = topologyrelations
        if(AssociatedIn.length) {
            const inData = Hzgctu.find(data => data.equipmentId === AssociatedIn[0])
            if(inData) {
                const pt1 = new McGePoint3d(points[0].x,points[0].y,0);
                const pt2 = new McGePoint3d(points[1].x,points[1].y,0);
                const line = new McDbLine(pt1, pt2)
                const res = line.getPointAtDist(3.4);
                if(res.ret){
                    points[0] = {
                        x: res.val.x,
                        y: res.val.y,
                        z: res.val.z
                    }
                }

            }
        }
        if(AssociatedOut.length) {
            const outData = Hzgctu.find(data => data.equipmentId === AssociatedOut[0])
            if(outData) {
                const pt1 = new McGePoint3d(points[pointLength - 1].x,points[pointLength - 1].y,0);
                const pt2 = new McGePoint3d(points[pointLength - 2].x,points[pointLength - 2].y,0);
                const line = new McDbLine(pt1, pt2)
                const res = line.getPointAtDist(3.4);
                if(res.ret){
                    points[pointLength - 1] = {
                        x: res.val.x,
                        y: res.val.y,
                        z: res.val.z
                    }
                }

            }
        }
        console.log('points',points)
        if(positionInfo.pointType === 'Line') {
            const options = {
                color: trueColor,
                legendtypekey: element.legendtypekey,
                xData: [
                    {
                        key: 'equipmentId',
                        value: element.equipmentId
                    },
                ]
            }
            drawLine(points, options).then((line) => {
                console.log('line', line)
            })
        } else if(positionInfo.pointType === 'MLine') {
            const options = {
                color: trueColor,
                equipmentId: element.equipmentId,
                legendtypekey: element.legendtypekey,
                xData: [
                    {
                        key: 'equipmentId',
                        value: element.equipmentId
                    },
                ]
            }
            drawMLine(points, options)
        }
    } else {

        let rd = 0
        if(positionInfo?.pointType === 'ParentMiddle') {
            const parentId = topologyrelations.AssociatedParent.length && topologyrelations.AssociatedParent[0]
            console.log('parentId', parentId)
            const parentData = Hzgctu.find(data => data.equipmentId === parentId);
            console.log('parentData', parentData)
            if(element.legendtypekey !== 'TY_JD') {
                rd = convertDegreesToRadians(-90);
            }
            if(parentData) {
                const parentLegendtypekey = parentData.legendtypekey
                const parentPoints = JSON.parse(parentData.equipmentInfo).positionInfo.points[0]?.XYZ[0].coordinate.split(',').map(Number);
                const [x, y, z] = parentPoints
                const pt = new McGePoint3d(x, y, z)
                if(parentLegendtypekey === 'TY_SNG') {
                    console.log('parentPoints', parentPoints)
                    const radian = convertDegreesToRadians(0);
                    console.log('radian', radian)
                    const newPt = calculateNewCoordinate(pt, 3.5, radian)
                    points = [newPt.x, newPt.y, newPt.z]
                    console.log('newPtpoints', points)
                }else{
                    points=parentPoints
                }
            } else {
                points = coordinate.split(',').map(Number);
            }
        } else {
            points = coordinate.split(',').map(Number);
        }


    // const angle = positionInfo.angle;
    // 解析 privatepropertys
    const privatepropertys =element.privatepropertys? JSON.parse(element.privatepropertys):element.privatepropertys;
    const attribDefList = []
        if(privatepropertys?.UserNumber) {
            attribDefList.push({
                name: '设备编号',
                value: privatepropertys.UserNumber
            })
        }
    //     const attribDefList = Object.keys(privatepropertys).map(key => ({
    //     name: key,
    //     value: privatepropertys[key]
    // }));

    // 构建 formData
    const formData = {
        spanClass: element.legendtypekey, // 使用 legendtypekey 作为 spanClass
        spanWidth: 50, // 假设缩放比例为 100，可以根据需要调整
        IsShowArr: [], // 假设所有属性都显示，可以根据需要调整
        points,
        rd,
        color: trueColor,
        legendtypekey: element.legendtypekey,
        xData: [
            {
                key: 'equipmentId',
                value: element.equipmentId
            },
        ]
    };

    // 图块文件名
    const fileName = `/assets/ModuleLegens/${element.legendDwg.split('.')[0]}.mxweb`;

    // 缩放比例
    const scale = 1.0; // 假设缩放比例为 1.0，可以根据需要调整

        console.log('attribDefList', attribDefList)
    // 调用 BlockFun 方法
    HzgctuBlockFun(formData, fileName, scale, attribDefList)
        .then(result => {
            console.log('图块插入成功:', result);
        })
        .catch(error => {
            console.error('图块插入失败:', error, element, formData);
        });
    }
}
catch (e) {
    console.error('huizhiTuError',e, element)
}


}

}

function deleteLayer(){
    let mxcad=MxCpp.getCurrentMxCAD()
    mxcad.newFile()
}
// SDK地图
function mapSDKLoad() {
    // window.parent.postMessage({
    //     params:{
    //         type:'Hzgctu'
    //     }
    // },"*")
    const mxdivElements = document.querySelector('#mxdiv');
    const newMapDiv = document.createElement('div');
    newMapDiv.id = 'mapBox'
    newMapDiv.style.width = '100%'
    newMapDiv.style.height = '100%'
    // newMapDiv.style.display = 'none'
    mxdivElements.prepend(newMapDiv)
    
    // 获取所有 class 为 "modelNav" 的元素
    const modelNavElements = document.querySelectorAll('.modelNav');
    // 遍历所有匹配的元素
    modelNavElements.forEach(modelNav => {
        // 创建一个新的 div 元素
        const newDiv = document.createElement('span');

        // 给新的 div 添加一些内容或类名（可以根据需要自定义）
        newDiv.style.position = 'relative'
        newDiv.style.display = 'inline-block'
        newDiv.style.padding = '0 1em'
        newDiv.style.zIndex = '0'
        newDiv.classList.add('nav'); // 可选的类名
        newDiv.innerHTML = '地图'; // 可选的内容

        // 在 modelNav 元素之前插入新的 div
        if (modelNav.firstChild) {
            modelNav.insertBefore(newDiv, modelNav.firstChild)
        }
        // modelNav.appendChild(newDiv);

        setTimeout(() => {
            // 获取当前点击元素的父元素 (modelNav)
            const parentElement = newDiv.parentNode;
            // 移除同一父元素下所有兄弟元素的 "nav-active" 类
            const siblings = parentElement.querySelectorAll('.nav');
            siblings.forEach(sibling => {
                sibling.classList.remove('nav-active');
            });
            newDiv.classList.add('nav-active');
            newMapDiv.style.display = ''
            mapOrCad = true
            initMap()
            document.querySelector('.mxbox').style.width = '100%'
            document.querySelectorAll('.bg-prominent').forEach(itemC => {
                console.log(itemC)
                itemC.style.setProperty('display', 'none', 'important')
            });
            document.querySelector('.d-flex').style.height = 'calc(-92px + 100vh)'
            document.querySelector('.v-toolbar__content').style.display = 'none'
            document.querySelector('.v-main').style.paddingTop = '0px'
        }, 1000);

        modelNav.addEventListener('click', function (e) {
            deleteLayer()
            const text = e.target.outerText
            console.log('text',text)
            // window.parent.postMessage({
            //     params: {
            //         type: 'Hzgctu',
            //     }
            // }, '*')
            if (text === '地图') {
                
                // 获取当前点击元素的父元素 (modelNav)
                const parentElement = newDiv.parentNode;

                // 移除同一父元素下所有兄弟元素的 "nav-active" 类
                const siblings = parentElement.querySelectorAll('.nav');
                siblings.forEach(sibling => {
                    sibling.classList.remove('nav-active');
                });
                newDiv.classList.add('nav-active');
                newMapDiv.style.display = ''
                mapOrCad = true
                // initMap()
                document.querySelector('.mxbox').style.width = '100%'
                document.querySelectorAll('.bg-prominent').forEach(itemC => {
                    console.log(itemC)
                    itemC.style.setProperty('display', 'none', 'important')
                });
                document.querySelector('.d-flex').style.height = 'calc(-92px + 100vh)'
                document.querySelector('.v-toolbar__content').style.display = 'none'
                document.querySelector('.v-main').style.paddingTop = '0px'

                const mxdivElements = document.querySelector('#mxdiv');
                mxdivElements.style.display = 'block'
                offCmdClick()
            } else {
                window.parent.postMessage({
                    params: {
                        type: 'Hzgctu',
                    }
                }, '*')
            //    调用绘制方法
                
                newDiv.classList.remove('nav-active');
                if (rastermap) {
                    newMapDiv.style.display = 'none'
                    // rastermap && rastermap.destroy();
                    //rastermap = null;
                }
                mapOrCad = false
                document.querySelector('.mxbox').style.width = 'calc(100% - 176px)'
                document.querySelectorAll('.bg-prominent').forEach(itemC => {
                    console.log(itemC)
                    itemC.style.setProperty('display', '', '')
                });
                document.querySelector('.d-flex').style.height = 'calc(-136px + 100vh)'
                document.querySelector('.v-toolbar__content').style.display = ''
                document.querySelector('.v-main').style.paddingTop = '48px'
                const mxdivElements = document.querySelector('#mxdiv');
                mxdivElements.style.display = 'flex'
                offCmdClick()
            }
        })
    });
    // 动态创建一个 <style> 标签
    const style = document.createElement('style');
    document.head.appendChild(style);

    // 插入 .nav::before 的 CSS 规则
    style.sheet.insertRule(`
            .nav::before {
              content: "";
              position: absolute;
              inset: 0;
              z-index: -1;
              background: rgb(var(--v-theme-nav_bg));
              border: 1px solid rgba(0, 0, 0, .4);
              border-bottom: none;
              border-radius: 0 0 .2em .2em;
              transform: perspective(.5em) rotateX(-5deg);
              transform-origin: center;
            }
        `, style.sheet.cssRules.length);
    // 插入 .nav-active 的 CSS 规则
    style.sheet.insertRule(`
            .nav.nav-active::before {
                background: rgba(var(--v-theme-nav_bg_active)); /* 当选中的元素改变背景色 */
            }
        `, style.sheet.cssRules.length);

    mxdivElements.style.display = 'block'
}

// 思极地图
function mapSJLoad() {
    window.SGMap.tokenTask
        .login("408b7cf3f08a3cc6bc8740239d3f4f43", "b69d88dfe8c339b4a1e03798c4de909c")
        .then(function () {
            window.SGMap.plugin(['SGMap.GeometryUtil', 'SGMap.GeocodingTask', 'SGMap.DirectionsTask', 'SGMap.DrawCircleHandler', 'SGMap.AutoCompletePlusTask', 'SGMap.DistrictTask']).then(function () {
                // 获取所有 class 为 "modelNav" 的元素
                const modelNavElements = document.querySelectorAll('.modelNav');
                // 遍历所有匹配的元素
                modelNavElements.forEach(modelNav => {
                    // 创建一个新的 div 元素
                    const newDiv = document.createElement('span');

                    // 给新的 div 添加一些内容或类名（可以根据需要自定义）
                    newDiv.style.position = 'relative'
                    newDiv.style.display = 'inline-block'
                    newDiv.style.padding = '0 1em'
                    newDiv.style.zIndex = '0'
                    newDiv.classList.add('nav'); // 可选的类名
                    newDiv.innerHTML = '地图'; // 可选的内容

                    // 在 modelNav 元素之前插入新的 div
                    modelNav.appendChild(newDiv);
                    modelNav.addEventListener('click', function (e) {
                        const text = e.target.outerText
                        if (text === '地图') {
                            // 获取当前点击元素的父元素 (modelNav)
                            const parentElement = newDiv.parentNode;

                            // 移除同一父元素下所有兄弟元素的 "nav-active" 类
                            const siblings = parentElement.querySelectorAll('.nav');
                            siblings.forEach(sibling => {
                                sibling.classList.remove('nav-active');
                            });
                            newDiv.classList.add('nav-active');
                            mapOrCad = true
                            initMap()
                            document.querySelector('.mxbox').style.width = '100%'
                            document.querySelectorAll('.bg-prominent').forEach(itemC => {
                                console.log(itemC)
                                itemC.style.setProperty('display', 'none', 'important')
                            });
                            document.querySelector('.d-flex').style.height = 'calc(-92px + 100vh)'
                            document.querySelector('.sgmap-canvas').style.height = '100%'
                            document.querySelector('.sgmap-canvas').style.width = '100%'
                            document.querySelector('.v-toolbar__content').style.display = 'none'
                            document.querySelector('.v-main').style.paddingTop = '0px'
                        } else {
                            newDiv.classList.remove('nav-active');
                            if (rastermap) {
                                rastermap && rastermap.remove();
                                rastermap = null;
                            }
                            mapOrCad = false
                            document.querySelector('.mxbox').style.width = 'calc(100% - 176px)'
                            document.querySelectorAll('.bg-prominent').forEach(itemC => {
                                console.log(itemC)
                                itemC.style.setProperty('display', '', '')
                            });
                            document.querySelector('.d-flex').style.height = 'calc(-136px + 100vh)'
                            document.querySelector('.v-toolbar__content').style.display = ''
                            document.querySelector('.v-main').style.paddingTop = '48px'
                        }
                    })
                });
                // 动态创建一个 <style> 标签
                const style = document.createElement('style');
                document.head.appendChild(style);

                // 插入 .nav::before 的 CSS 规则
                style.sheet.insertRule(`
            .nav::before {
              content: "";
              position: absolute;
              inset: 0;
              z-index: -1;
              background: rgb(var(--v-theme-nav_bg));
              border: 1px solid rgba(0, 0, 0, .4);
              border-bottom: none;
              border-radius: 0 0 .2em .2em;
              transform: perspective(.5em) rotateX(-5deg);
              transform-origin: center;
            }
        `, style.sheet.cssRules.length);
                // 插入 .nav-active 的 CSS 规则
                style.sheet.insertRule(`
            .nav.nav-active::before {
                background: rgba(var(--v-theme-nav_bg_active)); /* 当选中的元素改变背景色 */
            }
        `, style.sheet.cssRules.length);
            })
        })
}

// 地图创建
function initMap() {
    console.log('xtoken', xtoken)
    console.log('businessKey', sdkBusinessKey)
    console.log('businessNo', sdkBusinessNo)
    if (mapType === '01') {
        if (rastermap) return;
        rastermap = new HmsuiteMap({
            // 挂载dom
            container: "mapBox",
            // 中台 Token
            xtoken,
            // 中台应用 ID
            client_id: "",
            // 业务类型（10x：规划绘图 101:可研 20x：建设绘图 201:阳光业扩  202:初设  203:施工  2031:施工变更  204:竣工）
            businessKey: sdkBusinessKey,//20x,
            // 业务编号
            businessNo: sdkBusinessNo, //"999990",
            // 子业务编号
            subBusinessNo: engCode,
            // 数据类型（规划：plan 建设：design）
            dataType: parseInt(engStage) > 2 ? "design" : "plan",
            // 版本名称
            versionName: "",
            // 版本描述
            versionDesc: "",
            // 父任务的版本ID
            parentVersionId: "",
            // 场景（地图：map 地图浏览：mapView 专题图：thematic 专题图浏览：thematicView, 设计软件： designSoftware）
            scene: "designSoftware",
        });
        rastermap.on("complete", (params) => {
            if (lineInfos && lineInfos.length > 0) {
                // 加载馈线
                console.log('馈线', lineInfos)
                const feeders = lineInfos.filter(o=>o.isFeeder === '1').map(o=>o.lineCode)
                rastermap.loadFeederData(feeders, (resFeeder)=>{
                    if(resFeeder.status === "000000"){
                        const feeder = feeders.length > 0 ? feeders[0] : null
                        if(feeder){
                            rastermap.locateDevice(feeder);
                        }
                    }
                });
                // rastermap.loadStationData(["cmZvlvp0SPS8WhIP0x4KmQ"]);
            }
            //保存当前工程版本
            rastermap.getVersionData({
                    businessNo: sdkBusinessNo,
                    businessKey: sdkBusinessKey,
                },
                (params) => {
                    console.log('获取当前版本', params);
                    if (params.status === "000000") {
                        // 能查到版本
                        const versionID = params.result.find(o=>o.subBusinessNo === engCode)
                        window.parent.postMessage({
                            params: {
                                type: 'saveEngineeringVersion',
                                params: {id: taskid, versionId:versionID.versionId, isCover: 1}
                            }
                        }, '*')
                    } else {
                        sendMessage({type: 'showMessage', params: {msgType: 'error', content: params.message}})
                    }
                }
            );
        })
        SDKClickInfo.bind(rastermap, sendMessage)
        // // 绘制结束事件
        // rastermap.on("getCurrentVersionData", (params) => {
        //     console.log('getCurrentVersionData', params);
        //     window.parent.postMessage(
        //         {type: 'reply', params: params, mapSDKType: mapSDKType},
        //         '*'
        //     );
        // })
    } else {
        rastermap = new window.SGMap.Map({
            // 地图绑定的DOM元素ID
            container: "mxdiv",
            // 地图样式
            style: "aegis://styles/aegis/Streets-v2",
            // 默认缩放层级
            zoom: 5,
            // 地图中心点
            center: [114.06825863001939, 22.54283198132819],
            // 地图默认字体
            localIdeographFontFamily: "Microsoft YoHei"
        });
        rastermap.on("load", function () {
            const mxdiv = document.querySelector('.sgmap-canvas');
            mxdiv.style.backgroundColor = '#fff'
        });
    }
}
