import { saveAllInfos, getEquipmentState, undoEquipmentInfoByFlag } from "@/api/desginManage/draw.js";
import { voltageLevelData, getLegendTypeKeyByTowerType, getTowerArrangementKeyByLoop, getNoModuleTypeKey, getlegendType<PERSON>ey } from "@/views/pages/online/commonData.js";
import { ElMessage } from "element-plus";
import { calculateAngle } from "@/views/pages/online/geometricCalculation.js";
import { getPole, getNoModuleIdByVoltageLegendType } from "@/api/onlineDesign/matchData.js";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { generateUuid } from "@/utils";

/**
 * 撤销
 * @param {*} params 
 */
export const sdkUndo = async (params) => {
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  if (params.status === 'error') {
    ElMessage.warning(params.message)
  } else {
  let list = params.result.data
    let arr = list.map(item => ({
      equipmentId: item.DEV_ID,
      taskId: taskId,
      addFlag: item.addFlag === true || item.addFlag === false ? item.addFlag : 'null',
      className: item.CLASS_NAME
    }))
    undoEquipmentInfoByFlag(arr).then(res => {
      if (res.code == 200) {
        ElMessage.success('撤销成功')
      }
    })
  }

}

/**
 * 重做
 * @param {*} params sdk返回数据
 */
export const sdkRedo = (params) => {
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  if (params.status === 'error') {
    ElMessage.warning(params.message)
  } else {
    let list = params.result.data
  let arr = list.map(item => ({
    equipmentId: item.DEV_ID,
    taskId: taskId,
    addFlag: item.addFlag === true || item.addFlag === false ? item.addFlag : 'null',
    className: item.CLASS_NAME
  }))
  undoEquipmentInfoByFlag(arr).then(res => {
    if (res.code == 200) {
      ElMessage.success('重做成功')
    }
  })
  }
  
}


/**
 * 删除
 * @param {*} params 
 */
export const sdkRemoveDevice = (params) => {
  const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
  );
  if (params.status === 'error') {
    ElMessage.warning(params.message)
  } else {
  let list = params.result.data
  let arr = list.map(item => ({
    equipmentId: item.DEV_ID,
    taskId: taskId,
    addFlag: item.addFlag ?? 'null',
    className: item.CLASS_NAME
  }))
  undoEquipmentInfoByFlag(arr).then(res => {
    if (res.code == 200) {
      ElMessage.success('删除成功')
    }
  })
}
};