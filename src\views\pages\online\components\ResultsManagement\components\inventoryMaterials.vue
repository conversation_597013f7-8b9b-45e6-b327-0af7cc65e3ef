<template>
  <data-dialog
    @close="closeDialog"
    dataWidth="1200"
    v-if="appStore.mapIndex == '拆旧物资清册'"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        拆旧物资清册
      </h4>
    </template>
    <template #body>
      <div class="material">
        <el-button type="text" @click="add" icon="CirclePlus">添加</el-button>
        <el-button type="text" @click="deleted" icon="Delete">删除</el-button>
        <el-button icon="Paperclip" @click="exportExcel" type="text"
          >保存并生成报表</el-button
        >
        <el-table
          border
          class="input-table"
          size="small"
          @selection-change="handleSelectionChange2"
          :data="filteredTableData"
           :row-class-name="tableRowClassName"
          style="width: 100%; height: 400px"
        >
        <el-table-column
            :selectable="selectable"
            align="center"
            label="是否显示"
            type="selection"
            width="50px"
          ></el-table-column>
          <el-table-column
            align="center"
            type="index"
            label="序号"
            width="50px"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="materialcodeerp"
            label="ERP编码"
            width="150px"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="materialname"
            label="物料名称"
            width="150px"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="materialdescription"
            label="详细描述"
          >
          </el-table-column>
          <el-table-column
            align="center"
            prop="technicalprotocol"
            label="技术规范"
          >
          </el-table-column>
          <el-table-column align="center" prop="designunit" label="单位"   width="50px">
          </el-table-column>
          <el-table-column align="center" label="数量" width="100px">
            <template #default="scope">
              <div>
                <el-input
                  type="number"
                  min="1"
                  v-model="scope.row.num"
                ></el-input>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            width="200px"
            prop="state"
            label="状态"
          >
            <template #default="scope">
              <el-select style="width: 100%" v-model="scope.row.state">
                <el-option label="拆除利旧" value="RemoveUsing" />
                <el-option label="拆除报废" value="RemoveScrap" />
                <el-option label="拆除回收" value="RemoveAbandon" />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </template>
  </data-dialog>

  <el-dialog
    v-model="dialogVisible"
    :show-close="false"
    class="data-dialogWl"
    title="添加文件"
    width="1200"
  >
    <template #header="{ close, titleId, titleClass }">
      <div ref="header" :style="{ width: `100%` }" class="headerWl">
        <slot name="header">
          <div style="margin-left: 10px; line-height: 39px; font-weight: bold">
            请选择物料
          </div>
        </slot>
        <div class="close" @click="dialogVisible = false">X</div>
      </div>
    </template>

    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="物料库" name="first">
        <div class="photo-boxGhwl">
          <div class="photo-left">
            <div class="photo-border">
              <span
                style="
                  height: 30px;
                  line-height: 30px;
                  font-size: 14px;
                  margin-left: 10px;
                "
                >物料类别</span
              >
            </div>
            <div class="left-tree">
              <el-tree
                :data="treeData"
                :props="{ label: 'materialtypename', children: 'children' }"
                default-expand-all
                @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                  <span class="custom-node">
                    <img
                      :src="
                        data.children && data.children.length > 0
                          ? folderIcon
                          : fileIcon
                      "
                      alt="icon"
                      class="custom-icon"
                    />
                    <span
                      ref="label"
                      :style="{
                        width: '140px',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                      }"
                      class="custom-label"
                    >
                      <!-- 判断是否需要使用el-tooltip -->
                      <el-tooltip
                        v-if="node.level > 3"
                        :content="node.label"
                        class="box-item"
                        effect="dark"
                        placement="top"
                      >
                        {{ node.label }}
                      </el-tooltip>

                      <!-- 如果文本未溢出，直接展示文本 -->
                      <span v-else>{{ node.label }}</span>
                    </span>
                  </span>
                </template>
              </el-tree>
            </div>
          </div>
          <div class="photo-right">
            <div class="photo-border">
              <el-input
                v-model="form.input"
                class="input-with-select"
                placeholder="请输入图纸名称进行搜索"
                size="small"
                style="width: 400px; height: 29px"
              >
                <template #prepend>
                  <el-select
                    v-model="form.select"
                    placeholder=""
                    style="width: 115px; height: 29px"
                  >
                    <el-option label="物料名称" value="materialName" />
                    <el-option label="详细描述" value="materialDescription" />
                  </el-select>
                </template>
                <template #append>
                  <el-button icon="Search" @click="Search('wlk')" />
                </template>
              </el-input>
            </div>
            <div style="display: flex; border: 2px solid #badddd">
              <div class="photo-table">
                <el-table
                  :data="photoData"
                  border
                  style="margin-top: -2px; height: 400px; width: 100%"
                  @selection-change="handleSelectionChange1"
                >
                  <el-table-column
                    align="center"
                    type="selection"
                    width="55"
                  ></el-table-column>
                  <el-table-column
                    align="center"
                    label="ERP编码"
                    prop="materialcodeerp"
                  ></el-table-column>
                  <el-table-column
                    align="center"
                    label="物料名称"
                    prop="materialname"
                  ></el-table-column>
                  <el-table-column
                    align="center"
                    label="技术规范"
                    prop="technicalprotocol"
                  ></el-table-column>
                  <el-table-column
                    align="center"
                    label="详细描述"
                    prop="materialdescription"
                  ></el-table-column>
                  <el-table-column
                    align="center"
                    label="单位"
                    prop="designunit"
                  ></el-table-column>
                  <el-table-column
                    align="center"
                    label="单重"
                    prop="weight"
                  > 
                  <template #default="scope">
                  {{ scope.row.weight === 0 ? '-' : scope.row.weight }}
                </template>
                </el-table-column>
                  <el-table-column
                    align="center"
                    label="单价"
                    prop="taxprice"
                  ></el-table-column>

                  <!-- <el-table-column align="center" label="规格型号" prop="spec"></el-table-column> -->
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="自定义库" name="second">
        <div
          class="photo-boxGhwl"
          style="display: flex; flex-direction: column; padding: 0"
        >
          <div class="photo-border">
            <el-input
              v-model="form.input"
              class="input-with-select"
              placeholder="请输入图纸名称进行搜索"
              size="small"
              style="width: 400px; height: 29px"
            >
              <template #prepend>
                <el-select
                  v-model="form.select"
                  @change="selectChange"
                  placeholder=""
                  style="width: 115px; height: 29px"
                >
                  <el-option label="物料名称" value="materialName" />
                  <el-option label="详细描述" value="materialDescription" />
                </el-select>
              </template>
              <template #append>
                <el-button icon="Search" @click="Search('zdyk')" />
              </template>
            </el-input>
            <div style="margin-left: 10px;display: flex;align-items: center;">
              <el-button icon="CirclePlus" type="text" @click="addRow"
                >添加</el-button
              >
              <el-button icon="Delete" type="text" @click="del">删除</el-button>
              <el-button icon="Link" type="text" @click="createTemplate"
                >创建模板</el-button
              >
              <!-- <FileUpload @dataTreeList="dataTreeList"
                      :formData="{ prjTaskInfoId: taskId, specificationParentId: selectedNode.id }" ref="fileUp"
                      uploadFileUrl="/template/saveFile" :isShowTip="false" :fileType="[]"></FileUpload> -->
              
              <el-upload class="upload-demo"  action="test"
                    accept=".xlsx,.xls" :before-upload="beforeDesignUpload"
                    :show-file-list="false" :on-success="AnnexSuccess" :on-error="AnnexError"
                    :http-request="importTemplate">
                    <el-button icon="Paperclip" type="text" 
                >导入模板</el-button
              >
                  </el-upload>
            </div>
          </div>

          <div style="">
            <div class="material">
              <el-table
                :data="customData"
                border
                style="margin-top: -2px; height: 400px; width: 100%"
                @selection-change="handleSelectionChange"
              >
                <el-table-column
                  align="center"
                  type="selection"
                  width="55"
                ></el-table-column>
                <el-table-column align="center" label="ERP编码">
                  <template #default="scope">
                    <div v-if="!scope.row.manual">
                      {{ scope.row.materialcodeerp }}
                    </div>
                    <div v-else>
                      <el-input v-model="scope.row.materialcodeerp"></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="物料名称">
                  <template #default="scope">
                    <div v-if="!scope.row.manual">
                      {{ scope.row.materialname }}
                    </div>
                    <div v-else>
                      <el-input v-model="scope.row.materialname"></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="技术规范">
                  <template #default="scope">
                    <div v-if="!scope.row.manual">
                      {{ scope.row.technicalprotocol }}
                    </div>
                    <div v-else>
                      <el-input
                        v-model="scope.row.technicalprotocol"
                      ></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="详细描述">
                  <template #default="scope">
                    <div v-if="!scope.row.manual">
                      {{ scope.row.materialdescription }}
                    </div>
                    <div v-else>
                      <el-input
                        v-model="scope.row.materialdescription"
                      ></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="单位">
                  <template #default="scope">
                    <div v-if="!scope.row.manual">
                      {{ scope.row.designunit }}
                    </div>
                    <div v-else>
                      <el-input v-model="scope.row.designunit"></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="单重">
                  <template #default="scope">
                    <div v-if="!scope.row.manual">
                      {{ scope.row.weight }}
                    </div>
                    <div v-else>
                      <el-input v-model="scope.row.weight"></el-input>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="单价">
                  <template #default="scope">
                    <div v-if="!scope.row.manual">
                      {{ scope.row.taxRice }}
                    </div>
                    <div v-else>
                      <el-input v-model="scope.row.taxRice"></el-input>
                    </div>
                  </template>
                </el-table-column>

                <!-- <el-table-column align="center" label="规格型号" prop="spec"></el-table-column> -->
              </el-table>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div
        class="dialog-footer"
        style="margin-right: 10px; padding-bottom: 10px"
      >
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="TreeBtn">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {
  getAllMaterialTypes,
  listByUserAndKeywords,
  saveRemoveUsingMaterials,
  querycalculate,
  materialCustomImportExcel,
  removeMaterialCustomByIds,
  generatorCjwzmxReport
} from "@/api/insertSag/index.js";
import {
  getMaterials,
  addProjectmaterials,
  deleteProjectmaterials,
  exportProjectmaterials,
} from "@/api/desginManage/ResultsManagement.js";
import { useRoute } from "vue-router";
const { proxy } = getCurrentInstance();
import { inject,onMounted  } from "vue";
import folderIcon from "@/assets/images/file8.png";
import fileIcon from "@/assets/images/file5.png";
const route = useRoute();
const appStore = useAppStore();
const cadAppRef = inject("cadAppRef");
const taskId = route.query.id;
const dialogVisible = ref(false);
const treeData = ref([]);
const NodeList = ref({});
const photoData = ref([]);
const tableData = ref([]);
const tableData1 = ref([]);
const customData = ref([
  // {
  //   materialcodeerp: "测试数据",
  //   materialdescription: "测试数据",
  //   technicalprotocol: "测试数据",
  //   materialname: "测试数据",
  //   state: "2",
  // },
]);
const multipleSelection = ref([]);
const callChildC = inject("callChildC");
const closeDialog = () => {
  appStore.closeMapDialog()
};
const form = ref({
  select: "materialName",
  input: "",
});
const activeName = ref("first");

// watch(
//   () => appStore.mapIndex,
//   (newInfo, oldInfo) => {
//     if (newInfo === "拆旧物资清册") {
//       queryCalculateList();
//     }
//   },
//   { immediate: true } // 立即执行一次
// );
// onMounted(() => {
//   queryCalculateList()
// });
const queryCalculateList =()=>{
  let params={projectId:taskId,state:'Remove'}
  let arrTemp=[]
  querycalculate(params).then(res=>{
    console.log(res.data,'返回');
    res.data.forEach(item=>{
      arrTemp.push({
      removeusingId:item.removeusingId,
      materialcodeerp:item.materialCodeERP,
      materialname:item.materialName,
      technicalprotocol:item.technicalProtocol,
      materialdescription:item.spec,
      designunit:item.designUnit,
      num:item.num,
      materialsprojectid:item.materialsProjectID,
      fromSource:item.fromSource,
      addTag:0,
      state:item.state,
      remarks:item.remarks,
      materialTypeName:item.materialTypeName
    })
    })
   tableData.value=arrTemp
  })
}
const handleClick = (node) => {
  if(node.props.name=='second'){
    zdyList(form)
  }
  activeName.value = node.name;
};
const zdyList =(forms,type)=>{
  let params={}
  if(form.value.select=="materialName"){
    params={materialName:form.value.input}
  }else{
    params={materialDescription:form.value.input}
  }
  customData.value=[]
  if(type=='wlk'){
    console.log(params,'入参')
  }else{
    listByUserAndKeywords(params).then(res=>{
    console.log("🚀 ~ listByUserAndKeywords ~ res:", res)
  // customData.value=res.data
 
  res.data.forEach(item=>{
    customData.value.push( {
      id:item.id,
      materialcodeerp:item.materialCodeErp,
      materialdescription: item.materialDescription,
      technicalprotocol: item.technicalProtocol,
      materialname: item.materialName,
      state: "2",
      weight:item.weight,//单重
      taxRice:item.taxRice,//单价
      designunit:item.designUnit,//单位
      materialsprojectid:item.id
    },)
  })
})
  }


  
}
const selectChange=(e)=>{
  // zdyList(form)
}

//最外层table事件
const selectValue1 = ref([]);
const handleSelectionChange2 = (val) => {
  selectValue1.value = val;
};
const tableRowClassName=({row,rowIndex})=>{
  console.log('rowwwwwwww',row.addState);
  
  if (row.addState == 'new') {
          return 'warning-row';
        } 
        return '';
}
//添加
const add = () => {
  selectValue1.value = [];
  activeName.value = "first";
  customData.value = [
    // {
    //   materialcodeerp: "测试数据",
    //   materialdescription: "测试数据",
    //   technicalprotocol: "测试数据",
    //   materialname: "测试数据",
    //   state: "2",
    // },
  ];
  dialogVisible.value = true;
  getAllMaterialTypes().then((res) => {
    treeData.value = convertcurrent(res.data);
  });
};
//删除
const deleted = () => {
  if (selectValue1.value.length === 0) {
    proxy.$message.warning("请勾选至少一条数据");
  } else {
    // let arr = selectValue1.value.filter((item) => item.manual);
    // materialsprojectid
    console.log(selectValue1.value);
    proxy.$modal
      .confirm("确定要删除已勾选的材料嘛？", "提示", { type: "warning" })
      .then(() => {
        console.log(selectValue1.value)
        // tableData.value = tableData.value.filter((item) => !arr.includes(item));
        const selectedIds = new Set(selectValue1.value.map(item => item.materialsprojectid));
        console.log(selectedIds,'iidididi')
        // 过滤掉选中的项，并将未选中的项的 addTag 字段设置为 1
      
        tableData.value = tableData.value.map(item => {
        
          if (selectedIds.has(item.materialsprojectid)&&item.fromSource!='工程库') {
            if(item.num||item?.num==''||item?.num==null){
              item.num=1
            }
            return {
              ...item,
              addTag: 1 // 将未选中的项的 addTag 字段设置为 1
            };
             // 保持选中的项不变
          } else {
            return item;
          }
        });
        console.log(tableData.value)
        // exportExcel()
      });
  }
};
const filteredTableData = computed(() => {
  return tableData.value.filter(item => item.addTag !== 1);
});
//生成报表
const exportExcel = () => {
  console.log(tableData.value,"生成报表");
  const hasEmptyNum = tableData.value.some(item => item.num == null || item.num === "");
  if (hasEmptyNum) {
    proxy.$message.warning("数量不能为空");
    return; // 如果发现 num 为空，直接返回并提示
  }
  let params=[]
  tableData.value.forEach(item=>{
    console.log(item,'itemsssss')
    if(item.fromSource=="拆旧物资表"){
      params.push({  
        id:item.removeusingId?item.removeusingId:null,//有这个id是原有的，没有的是新增的
        materialsProjectID:item.materialsprojectid,
        prjTaskInfoId: taskId,  
        materialCodeErp: item.materialcodeerp,  
        designMaterialProjectId: item.materialsprojectid,  
        materialName: item.materialname, 
        materialSpec: item.spec?item.spec:item.materialdescription,  
        technicalProtocol: item.technicalprotocol,  
        designUnit: item.designunit?item.designunit:item.designunit,  
        quantity: item.num,  
        addTag: item.addTag?item.addTag:0,  
        materialState: item.state    
      })
    }
    
  })
  console.log(params);
  
  saveRemoveUsingMaterials(params).then(res=>{
        console.log(res);
          
            if(res.code==200){
              proxy.$message.success("操作成功");
              queryCalculateList()
            }
      })
  
  oniframeMessage({
    type: "cadPreview",
    content: "Mx_cjwz",
    formData: {
      tableData: JSON.stringify(tableData.value.filter(item => item.addTag !== 1)),
      countyOrganisationName: route.query.countyOrganisationName,
      projectName: route.query.projectName,
      stage: route.query.stage,
      proCode: route.query.proCode,
    },
  });
};
// 处理点击事件
const oniframeMessage = (param) => {
  const myiframe = appStore.iframeHide;
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener("message", handleMessage);
    // 添加新的事件监听器
    window.addEventListener("message", handleMessage);
  }
};
// 事件处理函数
const handleMessage = (event) => {
  console.log("🚀 ~ handleMessage ~ event:", event);

  if (event.data.type === "parentCad") {
    if (event.data.params.content === "拆旧物资清册") {
      const files = event.data.params.formData.files;
      const tableData = JSON.parse(event.data.params.formData.tableData)
     
      const tableDataNew = tableData.map((item, index) => {
        console.log(item,'拆旧项目lllllllll');
        
      return {
        xh:index + 1,
        wlmc:item.materialname,
        wlms:item.materialdescription,
        dw:item.designunit,
        sl:item.num,
        bz:item.remarks,
        wlId:item.materialsprojectid,   
      }
    })
    const fileFormData= new FormData()
 
    if(files.length==0){
      fileFormData.append('multipartFile', '')
      console.log(files,'filesfilesfilesfiles')
    }else{
      files.forEach(item => {
        fileFormData.append('multipartFile', item)
      })
    }
      fileFormData.append("cjwzmxList",JSON.stringify(tableDataNew))
      fileFormData.append("stage",route.query.stage)
      fileFormData.append("prjTaskInfoId",route.query.id)
      generatorCjwzmxReport(fileFormData).then(res1=>{
        console.log(res1,'dddddddd');
        if (callChildC) {
        callChildC();
      }
      })
      // console.log(files, 'filesfiles')
      // const tableData = JSON.parse(event.data.params.formData.tableData)
      // const tableDataNew = tableData.map(item => {
      //   return {
      //     gx:item.RodType,
      //     djzz:item.GrossWeightOfSingleBase,
      //     gtyd:item.GD1,
      //     lsjddqz:item.DJLS,
      //     gtedzz:item.GD2,
      //     ptz:item.PTZL,
      //     hdzz:item.CrossArm,
      //     js:item.Base,
      //     zz:item.FinalGrossWeight
      //   }
      // })
      // const fileFormData = new FormData()
      // files.forEach(item => {
      //   fileFormData.append('multipartFile', item)
      // })
      // fileFormData.append("gggmxList", JSON.stringify(tableDataNew));
      // fileFormData.append('prjTaskInfoId', route.query.id)
      // fileFormData.append('stage', route.query.stage)
      // generatorGggmxReport(fileFormData).then(res => {
      //   if (res.code === 200) {
      //     proxy.$message.success("保存成功");
      //   } else {
      //     proxy.$message.error(res.msg);
      //   }
      //   if (callChildC) {
      //     callChildC();
      //   }
      // })
    }
  }
};

//自定义 库
const selectValue = ref([]);
const handleSelectionChange = (val) => {
  multipleSelection.value = val.map((item) => {
    return {
      ...item,
      dataType: "Custom",
      materialsstate: "New",
      state: "RemoveUsing",
      fromSource:'拆旧物资表',
      addState:'new',
      
    };
  });
  console.log(multipleSelection.value,'测测');
  
};
//添加
const addRow = () => {
  let arr = {
    materialcodeerp: "",
    materialname: "",
    technicalprotocol: "",
    materialdescription: "",
    erpunit: "",
    manual: true,
    state: "2",
  };
  customData.value.unshift(arr);
  for (let index = 0; index < customData.value.length; index++) {
    const element = customData.value[index];
    if (element.manual) element.id = index;
  }
  console.log("🚀 ~ addRow ~ customData.value:", customData.value);
};
//删除
const del = () => {
  console.log(multipleSelection.value,'选中数据');
  
  if (multipleSelection.value.length === 0) {
    proxy.$message.warning("请勾选至少一条数据");
  } else {
    let arr = multipleSelection.value.filter((item) => item.manual);
    proxy.$modal
      .confirm("确定要删除已勾选的材料嘛？", "提示", { type: "warning" })
      .then(() => {
        let params=[]
        multipleSelection.value.forEach(item=>{
          params.push(item.id)
        })
        removeMaterialCustomByIds(params).then(res=>{
          console.log(res,'删除成');
          if(res.code==200){
            zdyList()
          }
        })
        // customData.value = customData.value.filter(
        //   (item) => !arr.includes(item)
        // );
        // console.log( customData.value,'删除后数据');
        // console.log( multipleSelection.value,'删除后数据2');
        
        // const ids = multipleSelectionWl.value.map(item => item.designresultsmaterialsid);
        // deleteProjectmaterials(ids).then(res => {
        //   if (res.code === 200) {
        //     proxy.$message.success("保存成功");
        //   } else {
        //     proxy.$message.error(res.msg);
        //   }
        //   listInfo()
        // })
      });
  }
};
//创建模板
const createTemplate = () => {  
  const link =document.createElement("a");
  link.href = `${import.meta.env.VITE_PUBLIC_FILE_PATH}cjwz.xls`;
  console.log(link.href)
  link.download = "拆旧物资模板.xls";
  document.body.appendChild(link);
  link.click();
  console.log(link)
  document.body.removeChild(link);
};
//导入模板
const importTemplate = (file) => {
console.log(file,'测试');
let formData = new FormData();
formData.append('file', file.file);
formData.append('materialState', "RemoveUsing");
materialCustomImportExcel(formData).then(res=>{
  console.log(res,'ceee');
  if(res.code === 200){
    zdyList()
  }
  
})
};
//上传前
const beforeDesignUpload= (file)=> {


}
const AnnexSuccess=()=>{
  console.log("上传成功");
}
const AnnexError=()=>{
  console.log("上传失败");
  
}
//物料库
const handleSelectionChange1 = (val) => {
  multipleSelection.value = val.map((item) => {
    return {
      ...item,
      dataType: "Custom",
      materialsstate: "New",
      state: "RemoveUsing",
      fromSource:"拆旧物资表",
        addState:'new'
    };
  });
};
const MaterialsList = () => {
  console.log(dynamicObject.value);
  let params ={materialname:dynamicObject.value.materialName,materialdescription:dynamicObject.value.materialDescription}
  getMaterials({
    materialstypekey: NodeList.value.materialstypekey,
    ...params,
  }).then((res) => {
    photoData.value = res.data;
  });
};
//确定
const TreeBtn = () => {
  tableData.value = [...multipleSelection.value, ...tableData.value];
  console.log("🚀 ~ TreeBtn ~ tableData.value:", tableData.value);
  dialogVisible.value = false;
};
const selectable = (row) => {
  return row.fromSource == "工程库" ? false : true;
};
const setRowClassName = ({ row }) => {
  return row.dataType !== "Custom" ? "non-custom-row" : "";
};
const Search = (type) => {
  zdyList(form,type)
  MaterialsList();
};
// tree
const handleNodeClick = (val) => {
  NodeList.value = val;
  if (!val.hasOwnProperty("children")) {
    MaterialsList();
  }
};
const dynamicObject = computed(() => {
  const obj = {
    [form.value.select]: form.value.input,
  };
  return Object.fromEntries(
    Object.entries(obj).filter(([key, value]) => key && value)
  );
});
const convertcurrent = (rows) => {
  let nodes = [];
  // 获取父级节点
  for (let i = 0; i < rows.length; i++) {
    let row = rows[i];
    if (!exists(rows, row.parentkey)) {
      nodes.push({
        materialstypekey: row.materialstypekey,
        materialtypename: row.materialtypename,
        state: row.state,
        attributes: row.materialstypekey,
      });
    }
  }

  let toDo = [...nodes]; // 初始化待处理数组
  while (toDo.length) {
    let node = toDo.shift(); // 父节点
    // 获取子节点
    for (let i = 0; i < rows.length; i++) {
      let row = rows[i];
      if (row.parentkey === node.materialstypekey) {
        let child = {
          materialstypekey: row.materialstypekey,
          materialtypename: row.materialtypename,
        };
        if (node.children) {
          node.children.push(child);
        } else {
          node.children = [child];
        }
        toDo.push(child);
      }
    }
  }
  return nodes;
};
/*传过来的list 按照父子关系生成树结构*/
const exists = (rows, parentkey) => {
  for (let i = 0; i < rows.length; i++) {
    if (rows[i].materialstypekey === parentkey) {
      return true;
    }
  }
  return false;
};
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "拆旧物资清册") {
      queryCalculateList()
    }
  }
);
</script>

<style lang="scss" scoped>
@use "../../index" as *;
::v-deep .el-table .warning-row {
    background: oldlace !important;
  }
.headerWl {
  // width: 378px;
  height: 39px;
  background: #{"var(--el-color-primary)"};
  border-radius: 3px 3px 0px 0px;
  line-height: 39px;
  font-weight: bold;
  font-size: 16px;
  color: #e8f2ee;
  position: relative;

  .close {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 0;
  }
}
::v-deep .non-custom-row {
  background-color: #ccc !important;
}
::v-deep .el-table tbody tr:hover > td {
  background-color: transparent !important;
}
.demo-tabs {
  padding: 0 20px;
}
.upload-demo{
  display: inline-block;
  margin-left: 12px;
}
</style>
