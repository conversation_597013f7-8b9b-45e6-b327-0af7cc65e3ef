import {MxFun} from "mxdraw";
import {MxCADUiPrKeyWord, MxCADUtility, McDb,  MxCpp, McDbText, MxCADUiPrPoint, MxCADSelectionSet} from 'mxcad'
async function ancillaryFacilitiesAnnotation(data) {
    console.log('ancillaryFacilitiesAnnotation', data)
    const getKey = new MxCADUiPrKeyWord
    getKey.setMessage("输入绘制类型")
    getKey.setKeyWords('[显示水泥制品标注(1)/隐藏水泥制品标注(2)]')
    const keyVal: string = await getKey.go()
    console.log('keyVal',keyVal)
    MxFun.postMessageToParentFrame({messageId: data.id, params: {keyVal}});

}

export function init() {
    MxFun.addCommand("Ancillary_Facilities_Annotation", ancillaryFacilitiesAnnotation);
}
