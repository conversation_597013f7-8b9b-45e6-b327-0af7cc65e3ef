<template>
  <!-- 四 标注管理 -->
    <!-- 1.标注设置管理 -->
    <data-dialog
      v-if="appStore.mapIndex == '标注设置管理'"
      dataWidth="620"
      @close="closeDialog"
    >
      <template #header>
        <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
          标注管理
        </h4>
      </template>
      <template #body>
        <div class="bz-line">
          <div class="bz-left">
            <span> 图元类型 </span>
            <div class="bz-border">
<!--              <el-checkbox label="水泥杆"></el-checkbox>-->
              <el-tree
                  :data="treeData"
                  :props="{ label: 'legendtypename', children: 'children' }"
                  default-expand-all
                  @node-click="handleNodeClick"
              >
                <template #default="{ node, data }">
                <span class="custom-node">
                  <span
                      ref="label"
                      :style="{
                      width: '140px',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }"
                      class="custom-label"
                  >
                    <!-- 判断是否需要使用el-tooltip -->
                    <el-tooltip
                        :content="node.label"
                        class="box-item"
                        effect="dark"
                        placement="top"
                    >
                      {{ node.label }}
                    </el-tooltip>
                  </span>
                </span>
                </template>
              </el-tree>            </div>
          </div>
          <div class="bz-right">
            <span> 标注设置 </span>
            <div>
              <el-table
                  ref="multipleTable"
                :data="data"
                  border
                class="input-table"
                row-key="PropertyKey"
                size="small"
                style="width: 500px"
              >
                <el-table-column :reserve-selection="true" align="center" label="是否显示" type="selection">
                </el-table-column>
                <el-table-column label="标注项目" prop="TagName"> </el-table-column>
                <el-table-column label="字体样式" prop="FontStyle">
                  <template #default="scope">
                    <div>
<!--                      <el-input v-model="scope.row.FontStyle" placeholder=""></el-input>-->
                      <el-select
                          v-model="scope.row.FontStyle"
                          filterable
                          placeholder="请选择"
                          size="small"
                          style="width: 65px"
                      >
                        <el-option
                            v-for="item in fontOption"
                            :key="item.value"
                            :label="item.text"
                            :value="item.value"
                        />
                      </el-select>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="字体颜色" prop="sk">
                  <template #default="scope">
                    <div>
<!--                      <el-input v-model="scope.row.FontColor" placeholder=""></el-input>-->
                      <el-color-picker v-model="scope.row.FontColor"/>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="字体高度" prop="FontHeight"> </el-table-column>
                <el-table-column label="宽度因子" prop="FontWidthFactor"> </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
        <div class="line" style="background: #e4f2f2; height: 50px">
          <div class="line-bnt" @click="submitBtn">确定</div>
          <div
            style="
              margin-left: 30px;
              background: #e4f2f2;
              border: 2px solid #199092;
              color: #199092;
              width: 84px;
              height: 30px;
              line-height: 27px;
              cursor: pointer;
              border-radius: 5px;
              font-size: 12px;
            "
            @click="appStore.mapIndex = ''"
          >
            取消
          </div>
        </div>
      </template>
    </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {
  getByLegendTypeKeyList,
  getLegendByTaskInfoId,
  getLegendtypekey, getTagManagementData,
  updateByTypeKey
} from "@/api/desginManage/AnnotationManagement.js";
import folderIcon from "@/assets/images/file8.png";
import fileIcon from "@/assets/images/file5.png";
let { proxy } = getCurrentInstance();
import {getFontList} from "@/api/taskBaseData/index.js";
import {useRoute} from "vue-router";
const route = useRoute();
const appStore = useAppStore();
const data = ref([])
const closeDialog = () => {
  appStore.mapIndex = "";
};
const input = ref()
const treeData = ref([])
const dataList = (arr) => {
  getLegendtypekey(arr).then(res => {
    treeData.value = res.data
  })
}
const fontOption = ref([])
const getFontOption = () => {
  getFontList().then((res) => {
    fontOption.value = res.data
  })
}
const handleNodeClick = (val) => {
  const params = {
    taskId: route.query.id,
    list: [val.legendtypekey]
  }
  getByLegendTypeKeyList(params).then(res => {
    // 切换的时候调用保存
    if (data.value.length > 0) {
      updateByTypeKey(data.value).then(res => {})
    }
    const result = res.data.flatMap(item => item.SettingList)
    result.forEach(row => {
      if (row.IsShow === '1') {
        proxy.$refs.multipleTable.toggleRowSelection(row, true);
      } else {
        proxy.$refs.multipleTable.toggleRowSelection(row, false);
      }
    });
    data.value = result
  })
}
const submitBtn = () => {
  if (data.value.length > 0) {
    data.value.forEach(itemF => {
      if (proxy.$refs.multipleTable.getSelectionRows().length > 0) {
        proxy.$refs.multipleTable.getSelectionRows().forEach(item => {
          if (itemF.PropertyKey === item.PropertyKey) {
            itemF.IsShow = '1'
          } else  {
            itemF.IsShow = '0'
          }
        })
      } else {
        itemF.IsShow = '0'
      }
    })
    updateByTypeKey(data.value).then(res => {
      if (res.code === 200) {
        proxy.$message.success("保存成功");
        oniframeMessage({
          type: "MsgCad",
          content: "Mx_bztlStyle",
          formData: {
            arr: JSON.stringify(data.value)
          }
        })
      } else {
        proxy.$message.error(res.msg);
      }
      appStore.mapIndex = "";
    })
  }
}
// 处理点击事件
function oniframeMessage(param) {
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
// 事件处理函数
const handleMessage = (event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '标注图例') {

    }
  }
}
const bztlData = () => {
  getLegendByTaskInfoId({ id: route.query.id }).then(res => {
    oniframeMessage({
      type: "onlycad",
      content: "Mx_bztl",
      options:{name:'JGFZH_Draw',tableData:res.data.filter(item => item !== null)} 
    })
    appStore.mapIndex=''
  })
}
const TagData = () => {
  getTagManagementData({ id: route.query.id }).then(res => {
    const result = res.data.map(item => item.legendtypekey);
    dataList(result)
  })
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "标注设置管理") {
      data.value = []
      TagData()
      getFontOption()
    } else if (newInfo == "标注图例") {
      bztlData()
    }
  }
);
</script>

<style lang="scss" scoped>
::v-deep .el-tree-node__content>.el-tree-node__expand-icon {
  padding: 0;
}
::v-deep .el-select {
    width: 257px;
    height: 27px;
  }

  ::v-deep .el-form-item.el-form-item--default {
    margin-bottom: 0;
    line-height: 0;
  }

  ::v-deep .el-form-item--default .el-form-item__content {
    line-height: normal;
  }
  ::v-deep .el-form-item__error {
    right: 22px !important;
    top: 12px !important;
    left: auto;
  }
  ::v-deep .el-select__placeholder {
    color: #282b33;
    font-weight: 400;
  }
  ::v-deep .el-input__inner {
    color: #282b33;
    font-weight: 400;
  }
  h4 {
    font-family: "Noto Sans SC", sans-serif;
  }
  .title-text {
    color: #008486;
    font-weight: bold;
    font-family: "Noto Sans SC", sans-serif;
  }
  .check_radio {
    ::v-deep .el-checkbox__label {
      color: #282b33;
      font-weight: 400;
    }
  }
  .line {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1px;
    text-align: center;
    font-family: "Noto Sans SC", sans-serif;
    font-weight: bold;
    :last-child {
      margin-bottom: 0;
    }
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }
    .tw-ipt {
      width: 84px;
      height: 40px;
      background: #b5dddd;
      //   font-weight: 550;
      font-size: 14px;
      color: #282b33;
      display: flex;
      justify-content: center;
      align-items: center;
      // span {
      //   width: 60px;
      // }
    }
    .radio-group {
      color: #282b33;
      font-weight: 400;
      ::v-deep .el-radio__label {
        color: #282b33;
        font-weight: 400;
      }
    }
    .checkbox-group {
      ::v-deep .el-checkbox__label {
        color: #282b33;
        font-weight: 400;
      }
    }

    .input-table {
      margin-top: -2px;
      margin-bottom: 1px;
    }
    .tw-sct {
      width: 105px;
      height: 40px;
      background: #e4f2f2;
      display: flex;
      justify-content: center;
      align-items: center;
      .in-item {
        width: 82px;
        height: 27px;
      }
    }
    .line-item {
      width: 84px;
      height: 40px;
      background: #b5dddd;
      //   font-weight: 550;
      font-size: 14px;
      color: #282b33;
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 10px;
      // span {
      //   width: 60px;
      // }
    }
    .line-input {
      // flex: 1;
      width: 294px;
      height: 40px;
      background: #e4f2f2;
      display: flex;
      justify-content: center;
      align-items: center;
      .in-item {
        width: 257px;
        height: 27px;
      }
    }
    .line-no-display {
      width: 294px;
      height: 40px;
      background: #e4f2f2;
      display: flex;
      align-items: center;
    }
    .radio-input {
      width: 294px;
      height: 60px;
      display: flex;
      background: #e4f2f2;
      flex-direction: column;
      .in-item {
        height: 20px;
        ::v-deep .el-input__wrapper {
          width: 150px;
        }
      }
    }
    //按钮
    .line-bnt {
      width: 84px;
      height: 30px;
      background: #0e8b8d;
      border-radius: 5px;
      color: white;
      line-height: 30px;
      cursor: pointer;
      font-size: 12px;
    }
  }

  // 标注样式
  .bz-line {
    display: flex;
    justify-content: flex-start;
    margin-bottom: 1px;
    font-family: "Noto Sans SC", sans-serif;
    font-weight: bold;
    height: 350px;
    .bz-left {
      width: 140px;
      margin-left: 10px;
      .bz-border {
        //padding: 0 10px;
        border: #69b5b6 2px solid;
        height: 320px;
        overflow: auto;
        overflow-x: hidden;
      }
    }
    .bz-right {
      width: 450px;
      margin-left: 10px;
      .input-table {
        margin-top: -2px;
        margin-bottom: 1px;
      }
      ::v-deep .el-table .el-table__header-wrapper th {
        background: #50a9aa !important;
        font-size: 14px;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }
  .material {
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }
    .active {
      background: #0e8b8d !important;
      color: white !important;
    }
  }
  .small-material {
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 11px !important;
      color: #ffffff;
      font-weight: 600;
    }
    ::v-deep .el-table td .el-table__cell div {
      font-size: 10px !important;
      font-weight: 600;
    }
  }
  .meter-draw {
    ::v-deep .el-table .el-table__header-wrapper th {
      background: #50a9aa !important;
      font-size: 14px;
      color: #ffffff;
      font-weight: 600;
    }
    .draw {
      font-size: 12px;
      color: #282b33;
      font-weight: bold;

      span {
        margin-left: 10px;
      }
    }
  }

  .draw-footer {
    margin: 0 10px 10px 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border: 1px solid #badddd;
  }
  .photo-box {
    padding: 10px;
    display: flex;
    height: 450px;
    justify-content: flex-start;
    background: #e4f2f2;
    .photo-left {
      width: 200px;
      height: 430px;
      .left-tree {
        border: 2px solid #badddd;
        padding: 10px;
        height: 400px;
        .el-tree {
          background: transparent;
        }
        ::v-deep .el-tree-node:focus > .el-tree-node__content {
          background: rgba(80, 169, 170, 0.4);
        }
        ::v-deep .el-tree-node :hover {
          background: rgba(80, 169, 170, 0.1) !important;
        }
      }
    }
    .photo-right {
      margin-left: 10px;
      width: 600px;
      height: 430px;
      .photo-border {
        height: 30px;
        width: 600px;
        border: 2px solid #badddd;
      }
      .photo-table {
        width: 350px;
        ::v-deep .el-table .el-table__header-wrapper th {
          background: #50a9aa !important;
          font-size: 14px;
          color: #ffffff;
          font-weight: 600;
        }
      }
      .photo-img {
        margin-top: -2px;
        width: 250px;
        background: white;
      }
    }
  }
  ::v-deep .el-checkbox__input.is-checked .el-checkbox__inner {
    background: #fff;
  }
  /* 修改Element UI中el-checkbox选中时的对号颜色 */
  ::v-deep .el-checkbox__inner:after {
    border-color: #07888a; /* 将对号颜色改为红色 */
  }

</style>
