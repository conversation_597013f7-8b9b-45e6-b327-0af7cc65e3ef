<template>
<data-dialog
    dataWidth="1000"
    dataHeight="400px"
    @close="closeDialog"
    v-if="appStore.mapIndex == '电缆附属设施统计表'"
  >
  <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        电缆附属设施统计表
      </h4>
    </template>
    <template #body>
      <div class="material">
        <div style="margin-left: 10px">
          <el-button icon="Paperclip" type="text" @click="exportExcel">生成报表</el-button>
        </div>
        <el-table :data="tableData" border class="input-table"  size="small" style="width: 100%">
          <el-table-column width="100" align="center" label="附件类别" prop="name" ></el-table-column>
          <el-table-column width="150" align="center" label="物料名称" prop="materialsName"></el-table-column>
          <el-table-column width="100" align="center" label="使用通道类别" prop="fstype"></el-table-column>
          <el-table-column width="100" align="center" label="数量" prop="num"></el-table-column>
          <el-table-column width="50" align="center" label="单位" prop="unit"></el-table-column>
          <el-table-column align="center" label="计算公式" prop="remark"></el-table-column>
        </el-table>
      </div>
    </template>
  </data-dialog>
</template>
<script setup> 
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import { useRoute } from "vue-router";
import {
  generatorDlfssstjReportTwo, getCableTable
} from "@/api/insertSag/index.js";
import { ElLoading } from 'element-plus'
import { inject } from 'vue';
const { proxy } = getCurrentInstance();
const route = useRoute();
const appStore = useAppStore();
const taskId = route.query.id;
const tableData=ref([])
const callChildC = inject('callChildC');

const closeDialog = () => {
  appStore.mapIndex = "";
};
const exportExcel = async () => {
  const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
  const tableDataNew = tableData.value.map((item) => {
    return {
      fjlb: item.name, //"附件类别"
      wlmc: item.materialsName, //"物料名称"
      sytdlb: item.fstype, //"使用通道类别"
      sl: item.num, //"数量"
      dw: item.unit, //"单位"
      jsgs: item.remark //"计算公式"
    }
  })
  const data = {
    dlfssstjjList: JSON.stringify(tableDataNew),
    stage: route.query.stage,
    prjTaskInfoId: route.query.id
  }
  console.log(tableDataNew)
  generatorDlfssstjReportTwo(data).then(res => {
    if (res.code === 200) {
      proxy.$message.success("保存成功");
      loading.close()
      if (callChildC) {
      callChildC();
    }
    } else {
      proxy.$message.error(res.msg);
      loading.close()
    }
   
  })
};

const queryList=()=>{
  getCableTable({taskId}).then(res=>{
    tableData.value = []
    for (let i = 0; i < res.data.length; i++) {
      if(res.data[i].num == 0){
        continue
      }
      let remark = ""
      let fstype = ""
      switch(res.data[i].name){
        case "电缆盖板" : fstype = "电缆沟敷设"; remark = "(每条电缆通道路径长度/x)(仅统计电缆沟敷设通道，其中，当通道内电缆根数等于1时，x为盖板长度；当通道内电缆根数大于1时，x为盖板宽度)";break;
        case "电缆警示牌" : fstype = "电缆沟敷设"; remark = "每条电缆通道路径长度/电缆警示牌间距(仅统计电缆沟敷设通道)";break;
        case "电缆标志桩" : fstype = "全部类别通道"; remark = "每条电缆通道路径长度/电缆标志桩间距";break;
        case "电缆警示带" : fstype = "全部类别通道"; remark = "每条电缆通道路径长度*该路径电缆根数";break;
        case "电缆管枕数量" : fstype = "新建的排管敷设B-1和低压排管方案"; remark = "((每条电缆通道路径长度/电缆管枕计算参数)+1)*敷设方式最大电缆根数(仅统计新建的排管敷设B-1方案和低压排管方案)";break;
        case "电缆墙支架（电缆墙担）" : fstype = "沿墙敷设"; remark = "如果电缆线路为沿墙敷设，则物料表中的电缆墙支架个数=电缆沿墙线路长度/用户设置的电缆墙间距，向上取整后即为电缆墙支架的个数";break;
        case "电缆挂钩" : fstype = "架空敷设"; remark = "如果电缆线路为架空敷设，则物料表中的电缆挂钩个数=电缆架空线路长度/用户设置的电缆挂钩间距，向上取整后即为电缆挂钩物料的个数";break;
      }
      tableData.value.push({
        materialsName: res.data[i].materialsName,
        name: res.data[i].name,
        num: res.data[i].num,
        unit: res.data[i].name === "电缆警示带" ? "米" : "个",
        remark,
        fstype
      })
    }
  })
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "电缆附属设施统计表") {
      queryList();
    }
  }
);
</script>
<style lang="scss" scoped>
@use '../../index' as *;
</style>