<script setup>
import {onUnmounted} from "vue";
import SolutionSelection from "./components/solution-selection.vue";
// import StandardDesignScheme from "./components/standard-design-scheme";
import IntervalSplice from "./components/interval-splice";
import ReuseCustomSchemes from "./components/reuse-custom-schemes";
import useAppStore from "@/store/modules/app.js";
const { proxy } = getCurrentInstance();
const appStore = useAppStore();
const form = reactive({
  radio: '1',
  switch: true
})
const rules = reactive({
  radio: [
    {
      required: true, message: '请选择', trigger: 'change'
    },
  ]
})

const onSubmit = () => {
  if (!form.radio) {
    console.log("请选择一个选项");
    return;
  }
  
  // if(appStore.sdkClassName!=='PWPowerCablePSR'&&appStore.sdkClassName!=='PWSwitchingStationPSR'&&appStore.sdkClassName!=='PWStationPSR'&&appStore.sdkClassName!=='PWBranchStationPSR'){
  //   console.log("🚀 ~ onSubmit ~ appStore.sdkClassName:", appStore.sdkClassName)
  //   proxy.$modal.msgWarning(`请选择站房!`);
  // }else{
  intervalSpliceVisible.value = true 
  // }
   // 根据 form.switch 的状态决定逻辑
   if (!form.switch) {
    solutionSelectionVisible.value = true; // 如果 switch 关闭，打开方案选择弹窗
    return;
  }
  // 如果 switch 开启，根据 radio 的值打开不同的弹窗
  switch (form.radio) {
    // case '1':
    //   intervalSpliceVisible.value = true // 这里可以根据需要添加弹窗逻辑
    //   break;
    case '2':
      standardDesignSchemeVisible.value = true; // 打开典设方案弹窗
      break;
    case '3':
      reuseCustomSchemesVisible.value = true; // 这里可以根据需要添加弹窗逻辑
      break;
    default:
      console.log("未知选项");
  }
}

// 方案选择弹窗
const solutionSelectionVisible = ref(false)

// 间隔拼接
const intervalSpliceVisible = ref(false)

// 调用典设方案弹窗
const standardDesignSchemeVisible = ref(false)

// 复用自定义方案弹窗
const reuseCustomSchemesVisible = ref(false)

// 复用自定义方案下一步
const reuseCustomSchemesSubmit = (e) => {
  intervalSpliceVisible.value = true
}

onUnmounted(() => {
  console.log('onUnmounted')
})
</script>

<template>
  <div class="" >
    <el-form ref="formRef" class="b-form" :model="form" :rules="rules" :show-message="false" label-width="auto">
      <el-row>
        <el-col>
          <el-form-item class="b-form-item" label="类型" prop="radio">
            <el-radio-group v-model="form.radio">
              <el-radio-button value="1">间隔拼接</el-radio-button>
              <!--              <el-radio-button value="2">调用典设方案</el-radio-button>-->
              <el-radio-button value="3">复用自定义方案</el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col>
          <el-form-item class="b-form-item" label="基于路径图元绘制">
            <el-switch v-model="form.switch"/>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="b-footer">
      <el-button type="primary" @click="onSubmit">确定</el-button>
    </div>

    <solution-selection v-if="solutionSelectionVisible" v-model:visible="solutionSelectionVisible"/>
    <interval-splice v-if="intervalSpliceVisible" v-model:visible="intervalSpliceVisible"/>
    <!--    <standard-design-scheme v-if="standardDesignSchemeVisible" v-model:visible="standardDesignSchemeVisible"/>-->
    <reuse-custom-schemes
        v-if="reuseCustomSchemesVisible"
        v-model:visible="reuseCustomSchemesVisible"
        @submit="reuseCustomSchemesSubmit"
    />
  </div>
</template>

<style scoped lang="scss">
</style>
