<template>
  <data-dialog
    v-if="appStore.mapIndex == '主要设备材料清单'"
    dataWidth="1200"
    :isFullscreenshow="true"
    @close="closeDialog"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        主要设备材料清单
      </h4>
    </template>
    <template #body>
      <div class="material" v-loading="loading">
        <div style="margin-left: 10px">
          <el-button icon="CirclePlus" type="text" @click="add">添加</el-button>
          <el-button icon="Delete" type="text" @click="del">删除</el-button>
          <el-button icon="" type="text" @click="save">保存</el-button>
          <el-button icon="Paperclip" type="text" @click="exportExcel"
            >生成报表</el-button
          >
          <el-button icon="Paperclip" type="text" @click="exportData"
            >查看规模统计</el-button
          >
        </div>
        <el-table
          :data="tableData"
          :row-class-name="setRowClassName"
          border
          class="input-table"
          height="70vh"
          size="small"
          style="width: 100%"
          @selection-change="handleSelectionChangeWl"
        >
          <el-table-column
            :selectable="selectable"
            align="center"
            label="是否显示"
            type="selection"
            width="50px"
          ></el-table-column>
          <el-table-column
            align="center"
            label="ERP编码"
            prop="materialcodeerp"
            width="150px"
          ></el-table-column>
          <el-table-column
            align="center"
            label="物料名称"
            prop="materialname"
          ></el-table-column>
          <el-table-column
            align="center"
            label="规格型号"
            prop="spec"
          ></el-table-column>
          <el-table-column
            align="center"
            label="物料描述"
            prop="materialdescription"
          ></el-table-column>
          <el-table-column
            align="center"
            label="技术规范"
            prop="technicalprotocol"
          ></el-table-column>
          <el-table-column
            align="center"
            label="单位"
            prop="erpunit"
          ></el-table-column>
          <el-table-column align="center" label="数量" prop="quantity">
            <template #default="scope">
              <el-input
                v-if="scope.row.dataType === 'Custom'"
                v-model="scope.row.quantity"
                placeholder=""
                style="width: 75px; height: 32px"
              />
              <span v-else>{{ scope.row.quantity }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="材料供给" prop="isdonor">
            <template #default="scope">
              <div v-if="scope.row.dataType === 'Custom'">
                <el-select
                  v-model="scope.row.isdonor"
                  placeholder=""
                  style="width: 75px; height: 32px"
                >
                  <el-option value="0" label="甲供" />
                  <el-option value="1" label="乙供" />
                </el-select>
              </div>
              <div v-else>
                <!-- 0: 甲供, 1:乙供 -->
                <span>{{
                  scope.row.isdonor == 0
                    ? "甲供"
                    : scope.row.isdonor == 1
                    ? "乙供"
                    : ""
                }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否备品备件" prop="">
            <template #default="scope">
              <el-select
                v-if="scope.row.dataType === 'Custom'"
                v-model="scope.row.isspare"
                placeholder=""
                style="width: 75px; height: 32px"
              >
                <el-option label="否" value="0" />
                <el-option label="是" value="1" />
              </el-select>
              <div v-else>
                <span v-if="scope.row.isspare === '1'">是</span>
                <span v-else>否</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            align="center"
            label="是否计入设备材料损耗"
            prop="sk"
          >
            <template #default="scope">
              <el-select
                v-if="scope.row.dataType === 'Custom'"
                v-model="scope.row.isloss"
                placeholder=""
                style="width: 75px; height: 32px"
              >
                <el-option label="否" value="0" />
                <el-option label="是" value="1" />
              </el-select>
              <div v-else>
                <span v-if="scope.row.isloss === '1'">是</span>
                <span v-else>否</span>
              </div>
            </template>
          </el-table-column>
          <!--默认否-->
          <!-- <el-table-column align="center" label="位置" prop="sk">
            <template #default="scope">
              <span v-if="scope.row.dataType === 'Custom'">无</span>
              <el-link v-else>定位图元</el-link>
            </template>
          </el-table-column> -->
        </el-table>
      </div>
    </template>
  </data-dialog>

  <el-dialog
    v-model="dialogVisible"
    :show-close="false"
    class="data-dialogWl"
    title="添加文件"
    width="1200"
  >
    <template #header="{ close, titleId, titleClass }">
      <div ref="header" :style="{ width: `100%` }" class="headerWl">
        <slot name="header">
          <div style="margin-left: 10px; line-height: 39px; font-weight: bold">
            更换物料
          </div>
        </slot>
        <div class="close" @click="dialogVisible = false">X</div>
      </div>
    </template>
    <div class="photo-boxGhwl">
      <div class="photo-left">
        <div class="photo-border">
          <span
            style="
              height: 30px;
              line-height: 30px;
              font-size: 14px;
              margin-left: 10px;
            "
            >物料类别</span
          >
        </div>
        <div class="left-tree">
          <el-tree
            :data="treeData"
            :props="{ label: 'materialtypename', children: 'children' }"
            default-expand-all
            @node-click="handleNodeClick"
          >
            <template #default="{ node, data }">
              <span class="custom-node">
                <img
                  :src="
                    data.children && data.children.length > 0
                      ? folderIcon
                      : fileIcon
                  "
                  alt="icon"
                  class="custom-icon"
                />
                <span
                  ref="label"
                  :style="{
                    width: '140px',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }"
                  class="custom-label"
                >
                  <!-- 判断是否需要使用el-tooltip -->
                  <el-tooltip
                    v-if="node.level > 3"
                    :content="node.label"
                    class="box-item"
                    effect="dark"
                    placement="top"
                  >
                    {{ node.label }}
                  </el-tooltip>

                  <!-- 如果文本未溢出，直接展示文本 -->
                  <span v-else>{{ node.label }}</span>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </div>
      <div class="photo-right">
        <div class="photo-border">
          <el-input
            v-model="form.input"
            class="input-with-select"
            placeholder="请输入图纸名称进行搜索"
            size="small"
            style="width: 400px; height: 29px"
          >
            <template #prepend>
              <el-select
                v-model="form.select"
                placeholder=""
                style="width: 115px; height: 29px"
              >
                <el-option label="规格型号" value="spec" />
                <el-option label="物料描述" value="materialdescription" />
                <el-option label="ERP编码" value="materialcodeerp" />
              </el-select>
            </template>
            <template #append>
              <el-button icon="Search" @click="Search" />
            </template>
          </el-input>
        </div>
        <div style="display: flex; border: 2px solid #badddd">
          <div class="photo-table">
            <el-table
              :data="photoData"
              border
              style="margin-top: -2px; height: 400px; width: 100%"
              @selection-change="handleSelectionChange"
            >
              <el-table-column
                align="center"
                type="selection"
                width="55"
              ></el-table-column>
              <el-table-column
                align="center"
                label="规格型号"
                prop="spec"
              ></el-table-column>
              <el-table-column
                align="center"
                label="物料描述"
                prop="materialdescription"
              ></el-table-column>
              <el-table-column
                align="center"
                label="ERP编码"
                prop="materialcodeerp"
              ></el-table-column>
              <el-table-column
                align="center"
                label="物料名称"
                prop="materialname"
              ></el-table-column>
              <el-table-column
                align="center"
                label="技术规范"
                prop="technicalprotocol"
              ></el-table-column>
              <el-table-column
                align="center"
                label="单位"
                prop="erpunit"
              ></el-table-column>
            </el-table>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div
        class="dialog-footer"
        style="margin-right: 10px; padding-bottom: 10px"
      >
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="TreeBtn">确定</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="dataScale"
    :show-close="true"
    class="data-dialogWl"
    title="规模统计"
    width="600"
  >
    <template #header>
      <div class="scale-header">
        <h4>规模统计</h4>
      </div>
    </template>
    <div style="height: 337px; margin-top: -16px">
      <div class="line">
        <div class="line-item">
          <span> 架空线路(km) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled 
            v-model="dataScaleInfo.overheadLine"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item">
          <span> 电力电缆(km) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.powerCable"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 低压线路(km) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.lowPressureLine"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item">
          <span> 低压电缆(km) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.lowPressureCable"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 配电变压器(台) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.transformer"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item">
          <span> 配电变压器(kVA) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.transformerVoltage"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 环网柜(台) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.ringMainUnit"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item">
          <span> DTU(台) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.dtu"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 普通开关(台) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.ordinarySwitch"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item">
          <span> 智能开关(台) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.intelligentSwitch"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 柱上变(台) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.columnVariation"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item">
          <span> 箱变(台) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.boxTransformer"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 水泥杆(基) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.cementPole"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item">
          <span> 钢管杆(基) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.steelPipePole"
            placeholder=""
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 铁塔(基) </span>
        </div>
        <div class="line-input">
          <el-input
            class="in-item"
            disabled
            v-model="dataScaleInfo.ironTower"
            placeholder=""
          ></el-input>
        </div>
        <div class="line-item"></div>
        <div class="line-input"></div>
      </div>
    </div>
  </el-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {
  getAllMaterialTypes,
  getMaterialsByIdAndUnitType,
  getMaterials,
  addProjectmaterials,
  deleteProjectmaterials,
  exportProjectmaterials,
  getScaleStatistics
} from "@/api/desginManage/ResultsManagement.js";
import {
    deleteByMaterialsProjectIdAndTaskId
} from "@/api/insertSag/index.js";
import { useRoute } from "vue-router";
import folderIcon from "@/assets/images/file8.png";
import fileIcon from "@/assets/images/file5.png";
import { isNumericString } from "@/utils/validate.js";
import { ElLoading } from 'element-plus'

const appStore = useAppStore();
const route = useRoute();
const closeDialog = () => {
  appStore.mapIndex = "";
};
const { cadAppSrc } = useCAdApp();

const cadIframeSrc = cadAppSrc({
  isPreview: "1",
});

const tableData = ref([]);
const dialogVisible = ref(false);
const dataScale = ref(false);
const dataScaleInfo = ref({});
const treeData = ref([]);
const form = ref({
  select: "spec",
  input: "",
});
let { proxy } = getCurrentInstance();

import { inject } from "vue";
import { useCAdApp } from "@/hooks/useCAdApp.js";
import { dataType } from "element-plus/es/components/table-v2/src/common.mjs";
import { vi } from "element-plus/es/locales.mjs";
const loading=ref(false)
const callChildC = inject("callChildC");
const selectable = (row) => {
  return row.dataType !== "Custom" ? false : true;
};
const setRowClassName = ({ row }) => {
  return row.dataType !== "Custom" ? "non-custom-row" : "";
};
const dynamicObject = computed(() => {
  const obj = {
    [form.value.select]: form.value.input,
  };
  return Object.fromEntries(
    Object.entries(obj).filter(([key, value]) => key && value)
  );
});
const Search = () => {
  MaterialsList();
};
const photoData = ref([]);
const NodeList = ref({});
/** 添加的物料 */
const multipleSelection = ref([]);
/** 选中的物料 */
const multipleSelectionWl = ref([]);
/** 最初添加的物料 */
const oldCustomData = ref([]);
const exportExcel = () => {
  oniframeMessage({
    type: "cadPreview",
    content: "Mx_zyclqd",
    formData: {
      content: "材料清单生成报表",
      tableData: JSON.stringify(tableData.value),
      countyOrganisationName: route.query.countyOrganisationName,
      projectName: route.query.projectName,
      stage: route.query.stage,
      proCode: route.query.proCode,
    },
  });
};
// 处理点击事件
const oniframeMessage = (param) => {
  // const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  const myiframe = appStore.iframeHide;
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener("message", handleMessage);
    // 添加新的事件监听器
    window.addEventListener("message", handleMessage);
  }
};
// 事件处理函数
const handleMessage = (event) => {
  if (
    event.data.type === "parentCad" &&
    event.data.params.content === "材料清单生成报表"
  ) {
    const loading = ElLoading.service({
        lock: true,
        text: '报表正在生成中，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    const files = event.data.params.formData.files;
    const tableData = JSON.parse(event.data.params.formData.tableData);
    const tableDataNew = tableData.map((item) => {
      return {
        xh: 0,
        dlms: "",
        zlms: "",
        xlms: "",
        wlbm: item.materialcodeerp,
        wlms: item.materialdescription,
        kzbm: "",
        kzms: "",
        jsgfid: item.technicalprotocol,
        dw: item.erpunit,
        sqdj: 0.0,
        sl: Number(item.quantity),
        wllx: "",
        dc: "",
        sfyfj: "",
        bz: "",
      };
    });
    const fileFormData = new FormData();
    files.forEach((item) => {
      fileFormData.append("multipartFile", item);
    });
    fileFormData.append("zyclqcList", JSON.stringify(tableDataNew));
    fileFormData.append("prjTaskInfoId", route.query.id);
    fileFormData.append("stage", route.query.stage);
    exportProjectmaterials(fileFormData).then((res) => {
      if (res.code === 200) {
        proxy.$message.success("保存成功");
        loading.close()
        if (callChildC) {
        callChildC();
      }
      } else {
        proxy.$message.error(res.msg);
        loading.close()
      }
    });
  }
};
const del = () => {
  console.log("删除");
  let arr = multipleSelectionWl.value.filter(
    (item) => item.dataType === "Custom"
  );
  if (!arr.length) {
    proxy.$message.warning("暂无可删除的数据");
    return;
  }
  const ids=arr.map(item=>item.materialsprojectid)
  proxy.$modal
    .confirm("确定要删除已勾选的材料嘛？", "提示", { type: "warning" })
    .then(() => {
      deleteByMaterialsProjectIdAndTaskId({materialsProjectId:ids,taskId:route.query.id}).then(res=>{
if(res.code==200){
      proxy.$message.success("删除成功");
      listInfo();
}
      }
      )
      // tableData.value = tableData.value.filter((item) => !arr.includes(item));
    });
};
const save = () => {
  if (!multipleSelectionWl.value.length) {
    proxy.$message.warning("请勾选至少一条数据");
    return;
  }
  if (
    multipleSelectionWl.value.some((item) => {
      if (!item.quantity || !isNumericString(item.quantity.toString())) {
        return true;
      }
    })
  ) {
    proxy.$message.warning("请填写数量");
    return;
  }
  const currentCustoms = multipleSelectionWl.value
    .filter((item) => item.dataType === "Custom")
    .map((item) => {
      // 使用对象解构分离出dataType和其他属性
      const { dataType, ...rest } = item;
      return {
        addTag: oldCustomData.value.some(
          (o) => o.materialsprojectid === rest.materialsprojectid
        )
          ? 2
          : 1,
        datatype: dataType,
        ...rest, // 展开剩余属性（不包含dataType）
        taskid: route.query.id,
        materialsprojectid: rest.materialsprojectid,
        materialsstate: "New",
        deltag: "0",
        mustneed: "1",
      };
    });
  const removeCustoms = oldCustomData.value
    .filter(
      (o) =>
        !currentCustoms.some(
          (c) => c.materialsprojectid === o.materialsprojectid
        )
    )
    .map((item) => {
      return {
        ...item,
        addTag: 0,
        datatype: "Custom",
        taskid: route.query.id,
        materialsstate: "New",
        deltag: "0",
        mustneed: "1",
      };
    });
  removeCustoms.forEach((item) => {
    currentCustoms.push(item);
  });
  console.log(currentCustoms);
  addProjectmaterials(currentCustoms).then((res) => {
    if (res.code === 200) {
      proxy.$message.success("保存成功");
    } else {
      proxy.$message.error(res.msg);
    }
    listInfo();
  });
};
const handleSelectionChangeWl = (val) => {
  console.log("🚀 ~ handleSelectionChangeWl ~ val:", val);
  multipleSelectionWl.value = val;
};
const handleSelectionChange = (val) => {
  multipleSelection.value = val.map((item) => {
    return {
      ...item,

      dataType: "Custom",
      materialsstate: "New",
    };
  });
};
const handleNodeClick = (val) => {
  NodeList.value = val;
  if (!val.hasOwnProperty("children")) {
    MaterialsList();
  }
};
const MaterialsList = () => {
  getMaterials({
    materialstypekey: NodeList.value.materialstypekey,
    ...dynamicObject.value,
  }).then((res) => {
    photoData.value = res.data;
  });
};
const TreeBtn = () => {
  multipleSelection.value.forEach((o) => {
    o.isdonor = o.isdonor.toString();
    o.isspare = "0";
    o.isloss = "0";
  });
  tableData.value = [...multipleSelection.value, ...tableData.value];
  dialogVisible.value = false;
};
const add = () => {
  dialogVisible.value = true;
  photoData.value = [];
  getAllMaterialTypes().then((res) => {
    treeData.value = convertcurrent(res.data);
  });
};
const exportData = () => {
  dataScale.value = true;
  getScaleStatistics({taskId: route.query.id}).then((res) => {
    dataScaleInfo.value = res.data
  });
};

const listInfo = () => {
  loading.value=true;
  getMaterialsByIdAndUnitType({
    projectId: route.query.id,
    unitType: "ERP",
    legendguidkeyListIn: appStore.property?appStore.property:[],
  }).then((res) => {
      if(res.code==200){
        tableData.value = res.data;
    oldCustomData.value = res.data
      .filter((item) => item.dataType === "Custom")
      .map((item) => item);
      }else{
        tableData.value=[]
        oldCustomData.value=[]
        proxy.$message.warning(res.msg);
      }
      loading.value=false;
  });
};
/*传过来的list 按照父子关系生成树结构*/
const exists = (rows, parentkey) => {
  for (let i = 0; i < rows.length; i++) {
    if (rows[i].materialstypekey === parentkey) {
      return true;
    }
  }
  return false;
};

const convertcurrent = (rows) => {
  let nodes = [];
  // 获取父级节点
  for (let i = 0; i < rows.length; i++) {
    let row = rows[i];
    if (!exists(rows, row.parentkey)) {
      nodes.push({
        materialstypekey: row.materialstypekey,
        materialtypename: row.materialtypename,
        state: row.state,
        attributes: row.materialstypekey,
      });
    }
  }

  let toDo = [...nodes]; // 初始化待处理数组
  while (toDo.length) {
    let node = toDo.shift(); // 父节点
    // 获取子节点
    for (let i = 0; i < rows.length; i++) {
      let row = rows[i];
      if (row.parentkey === node.materialstypekey) {
        let child = {
          materialstypekey: row.materialstypekey,
          materialtypename: row.materialtypename,
        };
        if (node.children) {
          node.children.push(child);
        } else {
          node.children = [child];
        }
        toDo.push(child);
      }
    }
  }
  return nodes;
};
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    if (newInfo == "主要设备材料清单") {
      listInfo();
    }
  }
);
</script>
<style lang="scss" scoped>
@use "../../index" as *;

.headerWl {
  // width: 378px;
  height: 39px;
  background: #{"var(--el-color-primary)"};
  border-radius: 3px 3px 0px 0px;
  line-height: 39px;
  font-weight: bold;
  font-size: 16px;
  color: #e8f2ee;
  position: relative;

  .close {
    cursor: pointer;
    position: absolute;
    right: 10px;
    top: 0;
  }
}
::v-deep .non-custom-row {
  background-color: #ccc !important;
  color:black !important;
}
::v-deep .el-table tbody tr:hover > td {
  background-color: transparent !important;
}

.line {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1px;
  text-align: center;
  font-family: "Noto Sans SC", sans-serif;
  font-weight: bold;

  :last-child {
    margin-bottom: 0;
  }

  .line-item {
    width: 184px;
    height: 40px;
    background: #b5dddd;
    //   font-weight: 550;
    font-size: 14px;
    color: #282b33;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    // span {
    //   width: 60px;
    // }
  }

  .line-input {
    // flex: 1;
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    justify-content: center;
    align-items: center;

    .in-item {
      width: 157px;
      height: 27px;
    }
  }

  .line-no-display {
    width: 294px;
    height: 40px;
    background: #e4f2f2;
    display: flex;
    align-items: center;
  }

  //按钮
  .line-bnt {
    width: 84px;
    height: 30px;
    background: #0e8b8d;
    border-radius: 5px;
    color: white;
    line-height: 30px;
    cursor: pointer;
    font-size: 12px;
  }
}

.scale-header {
  padding: 10px 16px;
  margin: 0px;
  h4 {
    margin: 0;
    font-size: 16px;
    color: var(--el-text-color-primary);
    font-weight: 600;
    line-height: 24px;
  }
}
</style>
