<script lang="jsx" setup>
import {Refresh} from "@element-plus/icons-vue";
import SingleEquipmentSelection from "./single-equipment-selection"
import {useDebounceFn} from '@vueuse/core'
import {getPropertyData, propertyChangeValue} from "@/api/onlineDesign/index.js";
import {useRoute} from "vue-router";
import {useFormData} from "./useFormData.js";
import {ElMessage} from "element-plus";
import useAppStore from "@/store/modules/app";
import { useIframeCommunication } from "@/hooks/useIframeCommunication";
const { sendMessage, cleanup } = useIframeCommunication();
const route = useRoute()
const taskId = route.query.id
const activeName = ref('1')
const switchValue = ref(false)
const loading = ref(false)
const appStore = useAppStore();
const {dataList, initData} = useFormData();
const originalData = ref([])
const legendguidObj=ref({})
const cadAppRef = inject("callChildC");
const props = defineProps({
  list: {
    type: Object,
  }
});
const getData = () => {
  currentData.value = ''
  loading.value = true
  const data = {
    "legendguidkeys": legendguidObj.value,
    "taskid": taskId
  }
  // if(!legendguidObj.value) return
  getPropertyData(data).then(async (res) => {
    if(res.data){
      res.data.forEach(item=>{
      if(item.sdkClassname){
        item.LegendAttributes.forEach(obj=>{
          obj.sdkClassname=item.sdkClassname
        })
      }
    })
    await initData(res.data)
    if(dataList.value[0].LegendName.includes('电缆段')){
      if(dataList.value[0].formGroupItems[0].children.length>0){
        dataList.value[0].formGroupItems[0].children.forEach(item=>{
          if(item.PropertyName=='替代档距（m）'){
            item.PropertyName='替代长度（m）'
          }
          if(item.PropertyName=='档距（m）'){
            item.PropertyName='长度（m）'
          }
        })
      }
    }
    originalData.value = dataList.value
    }else{
      await initData([])
      originalData.value=dataList.value
    }
    
    
  }).finally(() => {
    loading.value = false
  })
}

const editChange = async(value, e) => {
  const data = {
    "ObjectIdList": [e.ObjectId],
    AttributeKey: e.AttributeKey,
    AttributeValue: value,
    taskId: taskId,
    mark: e.mark,
    oldValue: e.AttributeValue
  }
  let sdkClassname='';
  if(e.sdkClassname){
    if(e.sdkClassname.includes('-')){
     sdkClassname=e.sdkClassname.split('-')[0]
  }else{
    sdkClassname=e.sdkClassname
  }
  }
  
   const options = {
          id: e.ObjectId,
          className: sdkClassname,
          rename: value,
          alisasName: '',
        };
        if(e.PropertyName=='编号'){
          await sendMessage(
    appStore.iframe,
    { type: "greeting", content: "SDK_rename_life", options },
    (res) => {
      if(res.params.status=='000000'){
         propertyChangeValue(data).then(res => {
    if (res.code === 200) {
      ElMessage.success('编号重名成功！')
    } else {
      ElMessage.error(res.msg)
    }
  })
      }else{
        ElMessage.error(res.params.message)
      }
    }
  );
        }else{
          propertyChangeValue(data).then(res => {
    if (res.code === 200) {
        // getData()
      ElMessage.success('修改成功！')
    } else {
      ElMessage.error(res.msg)
    }
  })
        }

}

// 防抖
const inputChange = useDebounceFn((value, e) =>{
  if(value!=e.mark){
    editChange(value, e)

  }
} , 500)


const currentData = ref()

const selectChange = (e) => {
  if (e) {
    dataList.value = originalData.value.filter(item => item.LegendTypeKey === e)
  } else {
    dataList.value = originalData.value

  }
}

const singleEquipmentSelectionVisible = ref(false)
const deviceId=ref('')
const linkChange = (e) => {
 deviceId.value= e.ObjectId
  singleEquipmentSelectionVisible.value = true
}
//生成随机数
function generateRandomNumber(digits = 5) {
  const min = 10 ** (digits - 1);
  const max = 10 ** digits - 1;
  return Math.floor(Math.random() * (max - min + 1)) + min;
}
watch(
  () => props.list,
  (newInfo, oldInfo) => {
    if(switchValue.value){
      if(JSON.stringify(newInfo)!==JSON.stringify(oldInfo)){
  legendguidObj.value=newInfo
  getData()
    }
    }else{
      if(JSON.stringify(newInfo)!==JSON.stringify(oldInfo)){
  legendguidObj.value=newInfo
    }
    }
    
  })
</script>

<template>
  <div class="property-container">
    <!-- item.LegendTypeKey+generateRandomNumber(5) -->
    <div class="top-box">
      <el-select v-model="currentData" clearable @change="selectChange">
        <el-option
          v-for="item in originalData"
          :key="item.LegendTypeKey"
          :label="item.LegendName"
          :value="item.LegendTypeKey"
        ></el-option>
      </el-select>
      <el-icon
        :class="{ 'is-loading': loading }"
        class="icon-button"
        color="#fff"
        size="24"
        @click="getData"
      >
        <Refresh />
      </el-icon>
      <el-switch
        v-model="switchValue"
        active-text="自动"
        inactive-text="自动"
        inline-prompt
      />
    </div>
    <el-collapse v-model="activeName" accordion>
      <el-collapse-item
        v-for="item in dataList"
        :key="`${item.LegendTypeKey}_${Date.now()}`"
        :name="item.LegendTypeKey"
        :title="item.LegendName"
      >
        <vxe-form
          :data="item.formData"
          title-align="right"
          title-overflow="title"
          title-width="30%"
        >
          <vxe-form-group
            v-for="group in item.formGroupItems"
            :title="group.title"
            span="24"
            title-bold
            vertical
          >
            <vxe-form-item
              v-for="formItem in group.children"
              :field="formItem.AttributeKey"
              :item-render="{}"
              :title="formItem.PropertyName"
              span="24"
            >
              <template #default="params">
                <el-input
                  v-if="formItem.editType === 0"
                  v-model="params.data[formItem.AttributeKey]"
                  :disabled="formItem.ReadOnly !== 0"
                  @blur="
                    inputChange(params.data[formItem.AttributeKey], formItem)
                  "
                />
                <el-select
                  v-if="[1, 5, 6].includes(formItem.editType)"
                  v-model="params.data[formItem.AttributeKey]"
                  :disabled="formItem.ReadOnly !== 0"
                  :multiple="formItem.editType === 6"
                  @change="
                    editChange(params.data[formItem.AttributeKey], formItem)
                  "
                >
                  <el-option
                    v-for="optionItem in formItem.options"
                    :label="optionItem.label"
                    :value="optionItem.value"
                  >
                  </el-option>
                </el-select>
                <el-link
                  v-if="formItem.editType === 3"
                  style="color: white"
                  @click="linkChange(formItem)"
                >
                  {{ formItem.AttributeValue }}
                </el-link>
              </template>
            </vxe-form-item>
          </vxe-form-group>
        </vxe-form>
      </el-collapse-item>
    </el-collapse>
    <single-equipment-selection
      v-if="singleEquipmentSelectionVisible"
      :id="deviceId"
      v-model:visible="singleEquipmentSelectionVisible"
    />
  </div>
</template>

<style lang="scss">
.property-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;

  .el-collapse-item__header {
    --el-collapse-header-bg-color: var(--sidebar-bg);
    --el-collapse-header-text-color: var(--el-color-white);
  }

  .el-collapse-item__wrap {
    --el-collapse-content-bg-color: var(--sidebar-bg);
  }

  .top-box {
    display: flex;
    gap: 8px;
    align-items: center;

    .icon-button {
      cursor: pointer;

      &:hover {
        color: var(--el-color-primary);
      }
    }
  }
  .vxe-form {
    color: white;
    background: var(--sidebar-bg);
  }
}
</style>
