import { saveAllInfos, getWellCount, getEquipmentState } from "@/api/desginManage/draw.js";
import { voltageLevelData, getLegendTypeKeyByTowerType, getTowerArrangementKeyByLoop, getNoModuleTypeKey, getlegendTypeKey } from "@/views/pages/online/commonData.js";
import { ElMessage } from "element-plus";
import { calculateAngle, comparePoints } from "@/views/pages/online/geometricCalculation.js";
import { getPole, getNoModuleIdByVoltageLegendType } from "@/api/onlineDesign/matchData.js";
import useProjectInfoStore from "@/store/modules/projectInfo.js";
import { getEquipmentModel } from "@/views/pages/online/saveModelInfo.js";

// 全局变量
let inListFlag = '';
let cableCount = 0
export const drawCableChannel = async (dataList, options, drawInfo, coordinate) => {
    console.log(dataList, options, drawInfo,'drawCableChannel')

    if (!dataList) return
    const projectInfoStore = useProjectInfoStore();
    const taskId = new URLSearchParams(new URL(window.location.href).search).get(
        "id"
    );
    //获取地理条件设置 气象区
    const geographicCondition = projectInfoStore.getGeographicConditionsSetting(taskId)
    //获取基础参数杆塔排列方式
    const cableSetting = projectInfoStore.getBaseSetting('cableSetting')
    const paramsSaveAllInfos = new Map()
    const towerAutoModuleInfos = []//记录杆塔坐标，自动选型参数

    try {
        // 封装通道
        const processSec = async (line, towerTopologyrelations) => {
            try {
                const coordinates = line.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g);
                if (!coordinates || coordinates.length < 2) {
                    console.warn("导线坐标格式异常", line.DEV_ID);
                    return;
                }
                const lineData = getEquipmentModel("MLine", coordinates, {
                    moduleId: options.channelModel,
                    legendTypeKey: drawInfo.sdkClassName.split('-')[1],
                    legendState: options.status,
                    legendGuidKey: line.DEV_ID,
                    engineeringId: taskId,
                })
                lineData.privatepropertys = JSON.stringify({
                    Span: line.LENGTH.toFixed(2),
                    TDDJ: line.LENGTH.toFixed(2),
                    LineNumber: options.lineName,
                })
                lineData.topologyrelations = JSON.stringify(towerTopologyrelations)
                paramsSaveAllInfos.set(line.DEV_ID, lineData);
            } catch (err) {
                console.error("通道处理异常", line.DEV_ID, err);
            }
        };
        // 封装电缆井
        const processWell = async (line, lineTopologyrelations, moduleId) => {
            console.log(line,'linelineline')
            let wellXY={
                X:line.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(" ")[0],
                Y:line.SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(" ")[1]
            }
            try {
                const lineData = getEquipmentModel("Point", `${wellXY.X} ${wellXY.Y}`, {
                    moduleId: moduleId ? moduleId : '',
                    legendTypeKey: getlegendTypeKey(line.CLASS_NAME),
                    legendState: options.status,
                    legendGuidKey: line.DEV_ID,
                    engineeringId: taskId,
                })
                lineData.privatepropertys = JSON.stringify({
                    LineNumber: options.lineName,
                    userNumber: options.cableCode2
                })
                lineData.topologyrelations = JSON.stringify(lineTopologyrelations)
                paramsSaveAllInfos.set(line.DEV_ID, lineData);
            } catch (err) {
                console.error("通道处理异常", line.DEV_ID, err);
            }
        };

        //封装HEAD_DEV_CLASS_NAME 等于"Well" 的  有可能有多个
        const headWell = async (line, name, headTopologyrelations, coordinate) => {
            console.log(coordinate,'coordinatecoordinatecoordinate')
            try {
                // 获取线路状态
                const legendState = await updateLineLegendState(line, taskId);
                const lineData = getEquipmentModel("Point", `${coordinate.X} ${coordinate.Y}`, {
                    legendTypeKey: getlegendTypeKey(name),
                    legendState: legendState,
                    legendGuidKey: line,
                    engineeringId: taskId,
                })
                lineData.topologyrelations = JSON.stringify(headTopologyrelations)
                paramsSaveAllInfos.set(line, lineData);
            } catch (err) {
                console.error("通道处理异常", line.DEV_ID, err);
            }
        };
        const cables = dataList.filter(o => o.CLASS_NAME === 'CableChannelSec');
        const cables1 = dataList.filter(o => o.CLASS_NAME === 'Well');
        if (cables[0].HEAD_DEV_CLASS_NAME === 'Well') {
            await getWellCount({ equipmentId: cables[0].HEAD_DEV_ID, taskId: taskId }).then(res => {
                cableCount = res.data + 1
            })
        }
        console.log(cables, cables1, '筛选')
        for (let i = 0; i < cables.length; i++) {
            // 通道-CableChannelSec拓扑关系初始化
            const towerTopologyrelations = {
                AssociatedIn: [],
                AssociatedOut: [],
                AssociatedParent: [],
                AssociatedChild: [],
                AssociatedLabel: [],
                AssociatedLabelOwner: [],
                AssociatedFile: [],
            };
            //井-Well拓扑关系
            const lineTopologyrelations = {
                AssociatedIn: [], // 进线拓扑
                AssociatedOut: [], // 出线拓扑
                AssociatedParent: [], //
                AssociatedChild: [], //
                AssociatedLabel: [], // 目前不涉及
                AssociatedLabelOwner: [], // 目前不涉及
                AssociatedFile: [], // 目前不涉及
            }
            const head_id = cables[i].HEAD_DEV_ID
            const tall_id = cables[i].TAIL_DEV_ID
            towerTopologyrelations.AssociatedIn.push(head_id)
            towerTopologyrelations.AssociatedOut.push(tall_id)
            if (cables1.length > 1) {
                lineTopologyrelations.AssociatedIn.push(cables[i].DEV_ID)
                if (i + 1 < cables.length) {
                    lineTopologyrelations.AssociatedOut.push(cables[i + 1].DEV_ID)
                }
            }
            await processSec(cables[i], towerTopologyrelations)
            if (cables1.length > i) {
                let p1, p2, p3, result1, result, angle, moduleId //p2 井 p1 前一段通道 p3后一段通道 result 比较p1 p2坐标是否一样 result1  比较p3 p2坐标是否一样
                if (i < cables1.length) p2 = ['' + cables1[i].X, '' + cables1[i].Y, 0]
                p1 = [...cables[i].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[1].split(' '), 0]
                if (i + 1 < cables.length) p3 = [...cables[i + 1].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[1].split(' '), 0]
                if (i < cables1.length) result = comparePoints(p2, p1)
                if (result) p1 = [...cables[i].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' '), 0]
                if (i + 1 < cables.length) result1 = comparePoints(p2, p3)
                if (result1) p3 = [...cables[i + 1].SHAPE.match(/(\d+\.\d+)\s+(\d+\.\d+)/g)[0].split(' '), 0]
                if (p3) angle = calculateAngle(p1, p2, p3) * 180 / Math.PI
                if (!p3) angle = 0
                if (cableCount == 3) {
                    moduleId = cableSetting.CableWells[0].SanTongWell
                    console.log(moduleId, 'SanTongWell', cableSetting.CableWells[0].SiTongWell)
                } else if (cableCount > 3) {
                    moduleId = cableSetting.CableWells[0].SiTongWell
                    console.log(moduleId, 'SiTongWell', cableSetting.CableWells[0].SiTongWell)
                } else {
                    if (angle >= 0 || angle < 180) {
                        moduleId = cableSetting.CableWells[0].ZhuanJiaoWell
                    } else if (angle === 180) {
                        moduleId = cableSetting.CableWells[0].ZhiXianWell
                    }
                }

                console.log(p2, p1, p3, '000')
                console.log(cables1[i], angle, moduleId, '角度')
                await processWell(cables1[i], lineTopologyrelations, moduleId)
            } else {

            }
            //井-Well拓扑关系
            const headTopologyrelations = {
                AssociatedIn: [], // 进线拓扑
                AssociatedOut: [], // 出线拓扑
                AssociatedParent: [], //
                AssociatedChild: [], //
                AssociatedLabel: [], // 目前不涉及
                AssociatedLabelOwner: [], // 目前不涉及
                AssociatedFile: [], // 目前不涉及
            }
            let headObj
            let tallObj
            if (cables[i].HEAD_DEV_CLASS_NAME && cables[i].TAIL_DEV_CLASS_NAME) {
                headObj = findValue(cables1, cables[i].HEAD_DEV_ID, '')
                tallObj = findValue(cables1, '', cables[i].TAIL_DEV_ID)
            } else if (cables[i].HEAD_DEV_CLASS_NAME && !cables[i].TAIL_DEV_CLASS_NAME) {
                headObj = findValue(cables, cables[i].HEAD_DEV_ID)
            }
            if (headObj) {
                const list = coordinate.find(o => o.DEV_ID === cables[i].HEAD_DEV_ID)
                console.log(list,'11111list')
                if (i == 0) {
                    headTopologyrelations.AssociatedOut.push(cables[0].DEV_ID)
                } else {
                    headTopologyrelations.AssociatedOut.push(cables[i].DEV_ID)
                    if (i < cables.length) headTopologyrelations.AssociatedIn.push(cables[i - 1].DEV_ID)
                }
                await headWell(cables[i].HEAD_DEV_ID, cables[i].HEAD_DEV_CLASS_NAME, headTopologyrelations, list)
            }
            if (tallObj) {
                headTopologyrelations.AssociatedIn = []
                if (i === cables.length - 1) {
                    headTopologyrelations.AssociatedOut = []
                }
                const list = coordinate.find(o => o.DEV_ID === cables[i].TAIL_DEV_ID)
                console.log(list,'22222list')
                headTopologyrelations.AssociatedIn.push(cables[i].DEV_ID)
                await headWell(cables[i].TAIL_DEV_ID, cables[i].TAIL_DEV_CLASS_NAME, headTopologyrelations, list)
            }
        }
        console.log(paramsSaveAllInfos, '处理完的数据')
        const finalData = [];
        paramsSaveAllInfos.forEach((value, key) => {
            finalData.push(value);
        });
        // 保存操作
        const res = await saveAllInfos(finalData);
        if (res.code === 200) {
            ElMessage.success("保存成功");

        } else {
            ElMessage.error(res.msg);
        }
    } catch (err) {
        console.error("主流程异常", err);
        ElMessage.error(`操作失败: ${err.message}`);
    }
}
function findValue(arr, head, tall) {
    let found
    if (head) {
        found = arr.find(item => item.DEV_ID === head)
    }
    if (tall) {
        found = arr.find(item => item.DEV_ID === tall)
    }
    if (!found) return true
    if (found) return false
}
// 优化后的状态获取函数
async function updateLineLegendState(id, taskId) {
    if (inListFlag === id) return "Original";

    try {
        const res = await getEquipmentState({ equipmentId: id, taskId });
        inListFlag = id;
        return res.data?.trim() || "Original";
    } catch (err) {
        console.error("状态获取失败", id, err);
        return "ErrorState";
    }
}