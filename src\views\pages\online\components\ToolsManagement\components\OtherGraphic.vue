<template>
  <data-dialog
    v-if="appStore.mapIndex == '绘制其他图元'"
    @close="closeDialog"
  >
    <template #header>
      <h4 style="margin-left: 10px; margin-top: 0px; line-height: 39px">
        绘制其他图元
      </h4>
    </template>
    <template #body>
      <div class="line">
        <div class="line-item">
          <span> 图元类别 </span>
        </div>
        <div class="line-input">
          <el-select v-model="OtherGraphic.tylb" class="in-item" placeholder="请选择" @change="tylbChange">
            <el-option
              v-for="item in OtherGraphicOption.tylbOption"
              :key="item.legendtypekey"
              :label="item.legendtypename"
              :value="item.legendtypekey"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 状态 </span>
        </div>
        <div class="line-input">
          <el-select v-model="OtherGraphic.state" class="in-item" placeholder="请选择">
            <el-option
              v-for="item in OtherGraphicOption.stateOption"
              :key="item.key"
              :label="item.value"
              :value="item.key"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 编号 <span style="color: red">*</span></span>
        </div>
        <div class="line-input">
          <el-input
            v-model="OtherGraphic.code"
            class="in-item"
            placeholder="请输入编号"
          ></el-input>
        </div>
      </div>
      <div class="line">
        <div class="line-item">
          <span> 物料\模块 </span>
        </div>
        <div class="line-input">
          <el-select v-model="OtherGraphic.wlMk" class="in-item" placeholder="请选择">
            <el-option
              v-for="item in OtherGraphicOption.wlMkOption"
              :key="item.moduleid"
              :label="item.modulename"
              :value="item.moduleid"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="line" style="background: #fff; height: 50px">
        <div class="line-bnt" @click="HzqtBtn">确定</div>
      </div>
    </template>
  </data-dialog>
</template>
<script setup>
import DataDialog from "@/components/DataDialog/index.vue";
import useAppStore from "@/store/modules/app.js";
import {getLegendCategories, legendTypeModules, legendTypesList} from "@/api/desginManage/ToolManagement.js";
import {saveAllInfo} from "@/api/desginManage/draw.js";
import {getByLegendTypeKeyList, updateByTypeKey} from "@/api/desginManage/AnnotationManagement.js";
import { getEquipmentModel } from "@/views/pages/online/saveModelInfo.js";
let { proxy } = getCurrentInstance();
const appStore = useAppStore();
const OtherGraphicOption = ref({
  tylbOption: [],
  wlMkOption: [],
  stateOption: [],
})
const OtherGraphic=ref({
  tylb:'',
  wlMk: '',
  code: '',
  state: ''
})
const closeDialog = () => {
  appStore.mapIndex = "";
};
const taskId = new URLSearchParams(new URL(window.location.href).search).get(
    "id"
);
// 图元类别事件
const tylbChange = () => {
  legendTypeModules({ legendTypeKey: OtherGraphic.value.tylb }).then(res => {
    if (res.code === 200) {
      OtherGraphicOption.value.wlMkOption = res.data
      OtherGraphic.value.wlMk = OtherGraphicOption.value.wlMkOption.length > 0 ? OtherGraphicOption.value.wlMkOption[0].moduleid : ''
    } else {
      console.log(res.msg)
    }
  })
  getLegendCategories({ legendTypeKey: OtherGraphic.value.tylb }).then(res => {
    if (res.code === 200) {
      OtherGraphicOption.value.stateOption = res.data[OtherGraphic.value.tylb]
      OtherGraphic.value.state = OtherGraphicOption.value.stateOption.length > 0 ? OtherGraphicOption.value.stateOption[0].key : ''
    } else {
      console.log(res.msg)
    }
  })
  OtherGraphic.value.code = ''
}
// 处理点击事件
function oniframeMessage(param) {
  const myiframe = document.querySelector("#myiframe").querySelector("iframe");
  if (myiframe && myiframe.contentWindow) {
    myiframe.contentWindow.postMessage(param, "*");
    // 先移除之前的事件监听器，防止重复绑定
    window.removeEventListener('message', handleMessage);
    // 添加新的事件监听器
    window.addEventListener('message', handleMessage);
  }
}
// 事件处理函数
const handleMessage = (event) => {
  if (event.data.type === 'parentCad') {
    if (event.data.params.content === '绘制其他图元') {
      dyjkFun(event.data.params.formData);
    }
  }
}
// 调用接口
const dyjkFun = (value) => {
  let obj = {
    UserNumber: OtherGraphic.value.code,
    State: OtherGraphic.value.state,
  }
  if (OtherGraphic.value.wlMk === 'TY_TYQ') {
    obj.DYJD = ''
  } else if (OtherGraphic.value.wlMk === 'TY_GTFZX') {
    obj.JGXX = ''
  }
  console.log('绘制其他图元', value)
  const params =  getEquipmentModel("Point", `${value.pointsXYZ.split(',')[0]} ${value.pointsXYZ.split(',')[1]}`, {
    moduleId: OtherGraphic.value.wlMk, // 顶层id
    legendTypeKey: OtherGraphic.value.tylb, // 图元类型
    legendGuidKey: value.legendGuidKey,
    legendState: OtherGraphic.value.state, // 状态
    engineeringId: taskId,
  })
  params.layName = OtherGraphic.value.tylb,
  params.handle = value.handle,
  params.privatepropertys = obj,
  saveAllInfo(params).then((res) => {
    if (res.code === 200) {
      proxy.$message.success("保存成功");
    } else {
      proxy.$message.error(res.msg);
    }
  });
}
const HzqtBtn = () => {
  if (!OtherGraphic.value.code) {
    proxy.$message.error('请输入编号！');
    return
  }
  const params = {
    taskId: taskId,
    list: [OtherGraphic.value.tylb]
  }
  getByLegendTypeKeyList(params).then(res => {
    const result = res.data.length > 0 ? res.data.flatMap(item => item.SettingList) : []
    oniframeMessage({
      type: "MsgCad",
      content: "Mx_hzqtty",
      formData: {
        stateText: OtherGraphicOption.value.stateOption.find(item => item.key === OtherGraphic.value.state).value,
        tylbText: OtherGraphicOption.value.tylbOption.find(item => item.legendtypekey === OtherGraphic.value.tylb).legendtypename,
        tylb: OtherGraphic.value.tylb,
        attribDefList: JSON.stringify([
          { name: '设备编号', value: OtherGraphic.value.code }
        ]),
        IsShowArr: result
      }
    })
  })
}
watch(
  () => appStore.mapIndex,
  (newInfo, oldInfo) => {
    console.log(newInfo, oldInfo);
    if (newInfo == "绘制其他图元") {
      legendTypesList().then(res => {
        if (res.code === 200) {
          OtherGraphicOption.value.tylbOption = res.data
          OtherGraphic.value.tylb = OtherGraphicOption.value.tylbOption.length > 0 ? OtherGraphicOption.value.tylbOption[0].legendtypekey : ''
          tylbChange()
        } else {
          console.log(res.msg)
        }
      })
    }
  }
);
</script>
<style lang="scss" scoped>
@use '../../index' as *;
</style>
